<template>
  <div class="customer-edit-form">
    <!-- 基本信息 -->
    <div class="form-section">
      <div class="section-title">基本信息</div>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
      >
        <el-form-item label="客户姓名" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入客户姓名"
            clearable
          />
        </el-form-item>

        <el-form-item label="联系电话" prop="contact">
          <el-input
            v-model="form.contact"
            placeholder="请输入联系电话"
            clearable
          />
        </el-form-item>

        <el-form-item label="备注信息">
          <el-input
            v-model="form.notes"
            placeholder="请输入备注信息（可选）"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 地址管理 -->
    <div class="form-section">
      <div class="section-title">
        <span>收货地址</span>
        <el-button
          type="primary"
          size="small"
          @click="showAddAddressDialog = true"
        >
          添加地址
        </el-button>
      </div>

      <div v-if="addresses.length === 0" class="empty-addresses">
        <el-icon><Location /></el-icon>
        <span>暂无地址，点击上方按钮添加</span>
      </div>

      <div v-else class="address-list">
        <div
          v-for="(address, index) in addresses"
          :key="address.id || index"
          class="address-item"
        >
          <div class="address-content">
            <div class="address-header">
              <span class="address-name">{{ address.recipient_name }}</span>
              <div class="address-actions">
                <el-tag v-if="address.is_default" size="small" type="primary">默认</el-tag>
                <el-button
                  v-else
                  type="text"
                  size="small"
                  @click="setDefaultAddress(address)"
                >
                  设为默认
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="editAddress(address, index)"
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="deleteAddress(index)"
                  style="color: var(--danger-color);"
                >
                  删除
                </el-button>
              </div>
            </div>
            <div class="address-details">{{ address.address_details }}</div>
            <div class="address-phone">{{ address.recipient_phone }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="$emit('cancel')" style="flex: 1;">
        取消
      </el-button>
      <el-button
        type="primary"
        @click="handleSubmit"
        :loading="loading"
        style="flex: 1;"
      >
        保存修改
      </el-button>
    </div>

    <!-- 添加/编辑地址对话框 -->
    <el-dialog
      v-model="showAddAddressDialog"
      :title="editingAddressIndex !== null ? '编辑地址' : '添加地址'"
      width="90%"
    >
      <el-form
        ref="addressFormRef"
        :model="addressForm"
        :rules="addressRules"
        label-position="top"
      >
        <el-form-item label="收件人姓名" prop="recipient_name">
          <el-input
            v-model="addressForm.recipient_name"
            placeholder="请输入收件人姓名"
          />
        </el-form-item>

        <el-form-item label="联系电话" prop="recipient_phone">
          <el-input
            v-model="addressForm.recipient_phone"
            placeholder="请输入联系电话"
          />
        </el-form-item>

        <el-form-item label="详细地址" prop="address_details">
          <el-input
            v-model="addressForm.address_details"
            placeholder="请输入详细地址"
            type="textarea"
            :rows="3"
          />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="addressForm.is_default">
            设为默认地址
          </el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="cancelAddressEdit">取消</el-button>
        <el-button type="primary" @click="saveAddress">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Location } from '@element-plus/icons-vue'
import { customerAPI, addressAPI } from '@/utils/api'

const props = defineProps({
  customer: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['success', 'cancel'])

const formRef = ref()
const addressFormRef = ref()
const loading = ref(false)
const showAddAddressDialog = ref(false)
const editingAddressIndex = ref(null)

const form = ref({
  name: '',
  contact: '',
  notes: ''
})

const addresses = ref([])

const addressForm = ref({
  recipient_name: '',
  recipient_phone: '',
  address_details: '',
  is_default: false
})

const rules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

const addressRules = {
  recipient_name: [
    { required: true, message: '请输入收件人姓名', trigger: 'blur' }
  ],
  recipient_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  address_details: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ]
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch {
    return
  }

  loading.value = true
  try {
    const result = await customerAPI.updateCustomer(props.customer.id, form.value)
    if (result.success) {
      emit('success', result.data)
    } else {
      ElMessage.error(result.error || '更新客户失败')
    }
  } catch (error) {
    ElMessage.error('更新客户失败')
  } finally {
    loading.value = false
  }
}

const editAddress = (address, index) => {
  editingAddressIndex.value = index
  addressForm.value = {
    recipient_name: address.recipient_name || '',
    recipient_phone: address.recipient_phone || '',
    address_details: address.address_details || '',
    is_default: address.is_default || false
  }
  showAddAddressDialog.value = true
}

const deleteAddress = async (index) => {
  const address = addresses.value[index]

  if (address.id) {
    // 如果是已保存的地址，需要调用API删除
    try {
      await ElMessageBox.confirm('确定要删除这个地址吗？', '删除地址', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const result = await addressAPI.deleteAddress(address.id)
      if (result.success) {
        addresses.value.splice(index, 1)
        ElMessage.success('地址删除成功')
      } else {
        ElMessage.error(result.error || '删除地址失败')
      }
    } catch {
      // 用户取消
    }
  } else {
    // 新添加但未保存的地址，直接从数组中移除
    addresses.value.splice(index, 1)
  }
}

const setDefaultAddress = async (address) => {
  if (address.id) {
    // 如果是已保存的地址，调用API设置默认
    try {
      const result = await addressAPI.setDefaultAddress(props.customer.id, address.id)
      if (result.success) {
        // 更新本地状态
        addresses.value.forEach(addr => {
          addr.is_default = addr.id === address.id
        })
        ElMessage.success('默认地址设置成功')
      } else {
        ElMessage.error(result.error || '设置默认地址失败')
      }
    } catch (error) {
      ElMessage.error('设置默认地址失败')
    }
  } else {
    // 新添加但未保存的地址，只更新本地状态
    addresses.value.forEach(addr => {
      addr.is_default = addr === address
    })
  }
}

const saveAddress = async () => {
  if (!addressFormRef.value) return

  try {
    await addressFormRef.value.validate()
  } catch {
    return
  }

  try {
    if (editingAddressIndex.value !== null) {
      // 编辑现有地址
      const address = addresses.value[editingAddressIndex.value]
      if (address.id) {
        // 更新已保存的地址
        const result = await addressAPI.updateAddress(address.id, addressForm.value)
        if (result.success) {
          Object.assign(address, addressForm.value)
          ElMessage.success('地址更新成功')
        } else {
          ElMessage.error(result.error || '更新地址失败')
          return
        }
      } else {
        // 更新未保存的地址
        Object.assign(address, addressForm.value)
      }
    } else {
      // 添加新地址
      const result = await addressAPI.createAddress(props.customer.id, addressForm.value)
      if (result.success) {
        addresses.value.push(result.data)
        ElMessage.success('地址添加成功')
      } else {
        ElMessage.error(result.error || '添加地址失败')
        return
      }
    }

    cancelAddressEdit()
  } catch (error) {
    ElMessage.error('保存地址失败')
  }
}

const cancelAddressEdit = () => {
  showAddAddressDialog.value = false
  editingAddressIndex.value = null
  addressForm.value = {
    recipient_name: '',
    recipient_phone: '',
    address_details: '',
    is_default: false
  }
}

const loadAddresses = async () => {
  try {
    const result = await addressAPI.getAddresses(props.customer.id)
    if (result.success) {
      addresses.value = result.data || []
    }
  } catch (error) {
    console.error('Load addresses error:', error)
  }
}

onMounted(() => {
  // 初始化表单数据
  form.value = {
    name: props.customer.name || '',
    contact: props.customer.contact || '',
    notes: props.customer.notes || ''
  }

  // 初始化地址数据
  if (props.customer.addresses) {
    addresses.value = [...props.customer.addresses]
  } else {
    loadAddresses()
  }
})
</script>

<style scoped>
.customer-edit-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.empty-addresses {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-secondary);
  background: var(--bg-page);
  border-radius: 8px;
  border: 1px dashed var(--border-color);
}

.empty-addresses .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.address-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.address-item {
  background: var(--bg-page);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.address-name {
  font-weight: 600;
  color: var(--text-primary);
}

.address-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.address-details {
  color: var(--text-primary);
  margin-bottom: 4px;
  line-height: 1.4;
}

.address-phone {
  color: var(--text-secondary);
  font-size: 14px;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  flex-shrink: 0;
  background: var(--bg-color);
  position: sticky;
  bottom: 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-primary);
}
</style>
