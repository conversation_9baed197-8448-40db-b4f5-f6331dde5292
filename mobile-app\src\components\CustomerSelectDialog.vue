<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="选择客户"
    width="90%"
  >
    <div class="customer-select">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索客户姓名或电话"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 快速创建按钮 -->
      <div class="quick-actions">
        <el-button 
          type="primary" 
          @click="showCreateForm = true"
          style="width: 100%"
        >
          <el-icon><Plus /></el-icon>
          新建客户
        </el-button>
      </div>

      <!-- 客户列表 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span style="margin-left: 8px;">加载中...</span>
      </div>

      <div v-else-if="filteredCustomers.length === 0" class="empty-state">
        <el-icon><User /></el-icon>
        <div class="empty-state-text">
          {{ searchQuery ? '未找到匹配的客户' : '暂无客户' }}
        </div>
      </div>

      <div v-else class="customer-list">
        <div 
          v-for="customer in filteredCustomers"
          :key="customer.id"
          class="customer-item touch-feedback"
          @click="selectCustomer(customer)"
        >
          <div class="customer-info">
            <div class="customer-name">{{ customer.name }}</div>
            <div class="customer-phone">{{ customer.phone }}</div>
            <div v-if="customer.address" class="customer-address">
              {{ customer.address }}
            </div>
          </div>
          <el-icon class="select-icon"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <!-- 创建客户表单 -->
    <el-dialog
      v-model="showCreateForm"
      title="新建客户"
      width="90%"
      append-to-body
    >
      <CustomerCreateForm 
        @success="handleCreateSuccess"
        @cancel="showCreateForm = false"
      />
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Plus, 
  User, 
  Loading,
  ArrowRight
} from '@element-plus/icons-vue'
import { customerAPI } from '@/utils/api'
import CustomerCreateForm from './CustomerCreateForm.vue'

const props = defineProps({
  modelValue: Boolean
})

const emit = defineEmits(['update:modelValue', 'select'])

const loading = ref(false)
const customers = ref([])
const searchQuery = ref('')
const showCreateForm = ref(false)

const filteredCustomers = computed(() => {
  if (!searchQuery.value.trim()) {
    return customers.value
  }
  
  const query = searchQuery.value.trim().toLowerCase()
  return customers.value.filter(customer => 
    customer.name.toLowerCase().includes(query) ||
    customer.phone.includes(query)
  )
})

const loadCustomers = async () => {
  loading.value = true
  try {
    const result = await customerAPI.getCustomers()
    if (result.success) {
      customers.value = result.data || []
    } else {
      ElMessage.error(result.error || '加载客户失败')
    }
  } catch (error) {
    ElMessage.error('加载客户失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索是通过计算属性实现的，这里不需要额外操作
}

const selectCustomer = (customer) => {
  emit('select', customer)
  emit('update:modelValue', false)
}

const handleCreateSuccess = (newCustomer) => {
  showCreateForm.value = false
  customers.value.unshift(newCustomer)
  ElMessage.success('客户创建成功')
  
  // 自动选择新创建的客户
  selectCustomer(newCustomer)
}

// 监听对话框打开状态
watch(() => props.modelValue, (newValue) => {
  if (newValue && customers.value.length === 0) {
    loadCustomers()
  }
})

onMounted(() => {
  if (props.modelValue) {
    loadCustomers()
  }
})
</script>

<style scoped>
.customer-select {
  max-height: 60vh;
  display: flex;
  flex-direction: column;
}

.search-bar {
  margin-bottom: 16px;
}

.quick-actions {
  margin-bottom: 16px;
}

.customer-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.customer-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.customer-item:hover {
  border-color: var(--primary-color);
  background: rgba(64, 158, 255, 0.05);
}

.customer-info {
  flex: 1;
  min-width: 0;
}

.customer-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.customer-phone {
  font-size: 14px;
  color: var(--primary-color);
  font-weight: 500;
  margin-bottom: 4px;
}

.customer-address {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.select-icon {
  color: var(--text-secondary);
  margin-left: 12px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--text-secondary);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.empty-state .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state-text {
  font-size: 14px;
}
</style>
