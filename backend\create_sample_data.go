package main

import (
	"fmt"
	"log"
	"shop-order-backend/internal/config"
	"shop-order-backend/internal/database"
	"shop-order-backend/internal/models"
	"time"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.Init(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 创建示例客户
	customers := []*models.Customer{
		{
			Name:      "张三",
			Contact:   "13800138001",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			Name:      "李四",
			Contact:   "13800138002",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			Name:      "王五",
			Contact:   "13800138003",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	for _, customer := range customers {
		// 检查客户是否已存在
		var existing models.Customer
		has, err := db.Where("contact = ?", customer.Contact).Get(&existing)
		if err != nil {
			log.Printf("Error checking customer %s: %v", customer.Name, err)
			continue
		}
		if !has {
			_, err = db.Insert(customer)
			if err != nil {
				log.Printf("Error creating customer %s: %v", customer.Name, err)
			} else {
				fmt.Printf("Created customer: %s\n", customer.Name)
			}
		} else {
			fmt.Printf("Customer %s already exists\n", customer.Name)
		}
	}

	// 创建示例商品
	products := []*models.Product{
		{
			Name:          "苹果",
			Category:      "水果",
			Price:         8.50,
			StockQuantity: 100,
			MinStockLevel: 10,
			StockUnit:     "斤",
			IsListed:      true,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		{
			Name:          "香蕉",
			Category:      "水果",
			Price:         6.00,
			StockQuantity: 80,
			MinStockLevel: 10,
			StockUnit:     "斤",
			IsListed:      true,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		{
			Name:          "牛奶",
			Category:      "饮品",
			Price:         12.00,
			StockQuantity: 50,
			MinStockLevel: 5,
			StockUnit:     "瓶",
			IsListed:      true,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		{
			Name:          "面包",
			Category:      "主食",
			Price:         15.00,
			StockQuantity: 30,
			MinStockLevel: 5,
			StockUnit:     "个",
			IsListed:      true,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		{
			Name:          "鸡蛋",
			Category:      "蛋类",
			Price:         18.00,
			StockQuantity: 5,
			MinStockLevel: 10,
			StockUnit:     "斤",
			IsListed:      true,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
	}

	for _, product := range products {
		// 检查商品是否已存在
		var existing models.Product
		has, err := db.Where("name = ?", product.Name).Get(&existing)
		if err != nil {
			log.Printf("Error checking product %s: %v", product.Name, err)
			continue
		}
		if !has {
			_, err = db.Insert(product)
			if err != nil {
				log.Printf("Error creating product %s: %v", product.Name, err)
			} else {
				fmt.Printf("Created product: %s\n", product.Name)
			}
		} else {
			fmt.Printf("Product %s already exists\n", product.Name)
		}
	}

	// 创建示例订单
	// 首先获取客户和商品ID
	var customerList []models.Customer
	err = db.Find(&customerList)
	if err != nil {
		log.Fatal("Failed to get customers:", err)
	}

	var productList []models.Product
	err = db.Find(&productList)
	if err != nil {
		log.Fatal("Failed to get products:", err)
	}

	if len(customerList) > 0 && len(productList) > 0 {
		// 创建几个示例订单
		orders := []*models.Order{
			{
				CustomerID:       customerList[0].ID,
				DeliveryAddress:  "北京市朝阳区建国路1号",
				DeliveryDatetime: time.Now().AddDate(0, 0, 1), // 明天
				TotalPrice:       45.50,
				OrderStatus:      "pending",
				PaymentStatus:    "unpaid",
				Notes:            "请准时送达",
				CreatedAt:        time.Now(),
				UpdatedAt:        time.Now(),
			},
			{
				CustomerID:       customerList[1].ID,
				DeliveryAddress:  "上海市浦东新区陆家嘴1号",
				DeliveryDatetime: time.Now(), // 今天
				TotalPrice:       32.00,
				OrderStatus:      "production",
				PaymentStatus:    "paid",
				Notes:            "",
				CreatedAt:        time.Now().Add(-2 * time.Hour),
				UpdatedAt:        time.Now(),
			},
			{
				CustomerID:       customerList[2].ID,
				DeliveryAddress:  "广州市天河区珠江新城1号",
				DeliveryDatetime: time.Now().AddDate(0, 0, -1), // 昨天
				TotalPrice:       28.50,
				OrderStatus:      "completed",
				PaymentStatus:    "paid",
				Notes:            "已完成",
				CreatedAt:        time.Now().Add(-24 * time.Hour),
				UpdatedAt:        time.Now().Add(-2 * time.Hour),
			},
		}

		for i, order := range orders {
			// 检查订单是否已存在
			var existing models.Order
			has, err := db.Where("customer_id = ? AND delivery_datetime = ?", order.CustomerID, order.DeliveryDatetime.Format("2006-01-02 15:04:05")).Get(&existing)
			if err != nil {
				log.Printf("Error checking order for customer %d: %v", order.CustomerID, err)
				continue
			}
			if !has {
				_, err = db.Insert(order)
				if err != nil {
					log.Printf("Error creating order for customer %d: %v", order.CustomerID, err)
					continue
				}
				fmt.Printf("Created order for customer ID: %d\n", order.CustomerID)

				// 创建订单项
				orderItems := []*models.OrderItem{
					{
						OrderID:      order.ID,
						ProductID:    productList[i%len(productList)].ID,
						Quantity:     2,
						PriceAtOrder: productList[i%len(productList)].Price,
						CreatedAt:    time.Now(),
					},
					{
						OrderID:      order.ID,
						ProductID:    productList[(i+1)%len(productList)].ID,
						Quantity:     1,
						PriceAtOrder: productList[(i+1)%len(productList)].Price,
						CreatedAt:    time.Now(),
					},
				}

				for _, item := range orderItems {
					_, err = db.Insert(item)
					if err != nil {
						log.Printf("Error creating order item: %v", err)
					}
				}
			} else {
				fmt.Printf("Order for customer ID %d already exists\n", order.CustomerID)
			}
		}
	}

	fmt.Println("Sample data creation completed!")
}
