package main

import (
	"fmt"
	"log"
	"shop-order-backend/internal/config"
	"shop-order-backend/internal/database"
	"shop-order-backend/internal/models"
	"time"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.Init(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 创建示例客户
	customers := []*models.Customer{
		{
			Name:      "张三",
			Contact:   "13800138001",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			Name:      "李四",
			Contact:   "13800138002",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			Name:      "王五",
			Contact:   "13800138003",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	for _, customer := range customers {
		// 检查客户是否已存在
		var existing models.Customer
		has, err := db.Where("contact = ?", customer.Contact).Get(&existing)
		if err != nil {
			log.Printf("Error checking customer %s: %v", customer.Name, err)
			continue
		}
		if !has {
			_, err = db.Insert(customer)
			if err != nil {
				log.Printf("Error creating customer %s: %v", customer.Name, err)
			} else {
				fmt.Printf("Created customer: %s\n", customer.Name)
			}
		} else {
			fmt.Printf("Customer %s already exists\n", customer.Name)
		}
	}

	// 创建示例商品
	products := []*models.Product{
		{
			Name:          "苹果",
			Category:      "水果",
			Price:         8.50,
			StockQuantity: 100,
			MinStockLevel: 10,
			StockUnit:     "斤",
			IsListed:      true,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		{
			Name:          "香蕉",
			Category:      "水果",
			Price:         6.00,
			StockQuantity: 80,
			MinStockLevel: 10,
			StockUnit:     "斤",
			IsListed:      true,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		{
			Name:          "牛奶",
			Category:      "饮品",
			Price:         12.00,
			StockQuantity: 50,
			MinStockLevel: 5,
			StockUnit:     "瓶",
			IsListed:      true,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		{
			Name:          "面包",
			Category:      "主食",
			Price:         15.00,
			StockQuantity: 30,
			MinStockLevel: 5,
			StockUnit:     "个",
			IsListed:      true,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		{
			Name:          "鸡蛋",
			Category:      "蛋类",
			Price:         18.00,
			StockQuantity: 5,
			MinStockLevel: 10,
			StockUnit:     "斤",
			IsListed:      true,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
	}

	for _, product := range products {
		// 检查商品是否已存在
		var existing models.Product
		has, err := db.Where("name = ?", product.Name).Get(&existing)
		if err != nil {
			log.Printf("Error checking product %s: %v", product.Name, err)
			continue
		}
		if !has {
			_, err = db.Insert(product)
			if err != nil {
				log.Printf("Error creating product %s: %v", product.Name, err)
			} else {
				fmt.Printf("Created product: %s\n", product.Name)
			}
		} else {
			fmt.Printf("Product %s already exists\n", product.Name)
		}
	}

	fmt.Println("Sample data creation completed!")
}
