<template>
  <div class="page">
    <div class="page-content">
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span style="margin-left: 8px;">加载中...</span>
      </div>

      <div v-else-if="!order" class="empty-state">
        <el-icon><Document /></el-icon>
        <div class="empty-state-text">订单不存在</div>
        <el-button @click="$router.go(-1)">返回</el-button>
      </div>

      <div v-else>
        <!-- 订单基本信息 -->
        <div class="card">
          <div class="card-header">
            <span>订单信息</span>
            <div class="header-tags">
              <el-tag
                :type="getStatusType(order.status)"
                size="small"
              >
                {{ getStatusText(order.status) }}
              </el-tag>
              <el-tag
                :type="getPaymentType(order.payment_status)"
                size="small"
                style="margin-left: 4px;"
              >
                {{ getPaymentText(order.payment_status) }}
              </el-tag>
            </div>
          </div>
          <div class="card-body">
            <div class="info-grid">
              <div class="info-item">
                <span class="label">订单号:</span>
                <span class="value">{{ order.id }}</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间:</span>
                <span class="value">{{ formatDateTime(order.created_at) }}</span>
              </div>
              <div class="info-item">
                <span class="label">交付日期:</span>
                <span class="value">{{ formatDate(order.delivery_date) }}</span>
              </div>
              <div class="info-item">
                <span class="label">订单金额:</span>
                <span class="value amount">¥{{ order.total_amount }}</span>
              </div>
              <div class="info-item">
                <span class="label">付款状态:</span>
                <span class="value">{{ getPaymentText(order.payment_status) }}</span>
              </div>
              <div v-if="order.payment_amount" class="info-item">
                <span class="label">已付金额:</span>
                <span class="value amount">¥{{ order.payment_amount }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 客户信息 -->
        <div class="card">
          <div class="card-header">
            <span>客户信息</span>
          </div>
          <div class="card-body">
            <div class="info-grid">
              <div class="info-item">
                <span class="label">客户姓名:</span>
                <span class="value">{{ order.customer_name }}</span>
              </div>
              <div class="info-item">
                <span class="label">联系电话:</span>
                <span class="value">
                  <a :href="`tel:${order.customer_phone}`" class="phone-link">
                    {{ order.customer_phone }}
                  </a>
                </span>
              </div>
              <div v-if="order.customer_address" class="info-item full-width">
                <span class="label">收货地址:</span>
                <span class="value">{{ order.customer_address }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品清单 -->
        <div class="card">
          <div class="card-header">
            <span>商品清单</span>
            <span class="item-count">{{ order.items?.length || 0 }} 种商品</span>
          </div>
          <div class="card-body">
            <div v-if="!order.items || order.items.length === 0" class="empty-state">
              <el-icon><Goods /></el-icon>
              <div class="empty-state-text">暂无商品</div>
            </div>
            <div v-else class="item-list">
              <div 
                v-for="item in order.items"
                :key="item.id"
                class="item-row"
              >
                <div class="item-info">
                  <div class="item-name">{{ item.product_name }}</div>
                  <div class="item-spec">
                    {{ item.quantity }} {{ item.unit }} × ¥{{ item.unit_price }}
                  </div>
                </div>
                <div class="item-amount">
                  ¥{{ (item.quantity * item.unit_price).toFixed(2) }}
                </div>
              </div>
              
              <div class="total-row">
                <div class="total-label">合计</div>
                <div class="total-amount">¥{{ order.total_amount }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div v-if="order.notes" class="card">
          <div class="card-header">
            <span>备注信息</span>
          </div>
          <div class="card-body">
            <div class="notes-content">{{ order.notes }}</div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-section">
          <!-- 付款操作 -->
          <div v-if="order.payment_status !== 'paid'" class="payment-actions">
            <el-button
              type="success"
              @click="markAsPaid"
              :loading="updating"
              style="width: 100%; margin-bottom: 12px;"
            >
              标记为已付款
            </el-button>
          </div>

          <!-- 状态操作 -->
          <el-button
            v-if="order.status === 'pending'"
            type="primary"
            @click="updateOrderStatus('production')"
            :loading="updating"
            style="width: 100%; margin-bottom: 12px;"
          >
            开始生产
          </el-button>

          <el-button
            v-if="order.status === 'production'"
            type="primary"
            @click="updateOrderStatus('delivery')"
            :loading="updating"
            style="width: 100%; margin-bottom: 12px;"
          >
            开始配送
          </el-button>

          <el-button
            v-if="order.status === 'delivery'"
            type="success"
            @click="updateOrderStatus('completed')"
            :loading="updating"
            style="width: 100%; margin-bottom: 12px;"
          >
            完成订单
          </el-button>

          <div class="button-group">
            <el-button
              @click="editOrder"
              :disabled="order.status === 'completed'"
              style="flex: 1;"
            >
              编辑订单
            </el-button>

            <el-button
              type="danger"
              @click="cancelOrder"
              :disabled="order.status === 'completed' || order.status === 'cancelled'"
              :loading="updating"
              style="flex: 1;"
            >
              取消订单
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Document, 
  Goods, 
  Loading 
} from '@element-plus/icons-vue'
import { orderAPI } from '@/utils/api'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const updating = ref(false)
const order = ref(null)

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'production': 'primary',
    'delivery': 'info',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

const getPaymentType = (paymentStatus) => {
  const paymentMap = {
    'paid': 'success',
    'unpaid': 'warning',
    'partial': 'info'
  }
  return paymentMap[paymentStatus] || 'info'
}

const getPaymentText = (paymentStatus) => {
  const paymentMap = {
    'paid': '已付款',
    'unpaid': '未付款',
    'partial': '部分付款'
  }
  return paymentMap[paymentStatus] || '未知'
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatDateTime = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const loadOrder = async () => {
  loading.value = true
  try {
    const result = await orderAPI.getOrder(route.params.id)
    if (result.success) {
      order.value = result.data
    } else {
      ElMessage.error(result.error || '加载订单失败')
    }
  } catch (error) {
    ElMessage.error('加载订单失败')
  } finally {
    loading.value = false
  }
}

const markAsPaid = async () => {
  try {
    await ElMessageBox.confirm('确定要标记为已付款吗？', '付款确认', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'success'
    })

    updating.value = true
    const result = await orderAPI.updateOrder(order.value.id, {
      payment_status: 'paid',
      payment_amount: order.value.total_amount
    })

    if (result.success) {
      order.value.payment_status = 'paid'
      order.value.payment_amount = order.value.total_amount
      ElMessage.success('已标记为已付款')
    } else {
      ElMessage.error(result.error || '更新付款状态失败')
    }
  } catch {
    // 用户取消
  } finally {
    updating.value = false
  }
}

const updateOrderStatus = async (newStatus) => {
  const statusMessages = {
    'production': '开始生产',
    'delivery': '开始配送',
    'completed': '完成订单'
  }

  const statusTexts = {
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成'
  }

  try {
    await ElMessageBox.confirm(
      `确定要${statusMessages[newStatus]}吗？`,
      statusMessages[newStatus],
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: newStatus === 'completed' ? 'success' : 'warning'
      }
    )

    updating.value = true
    const result = await orderAPI.updateOrder(order.value.id, {
      status: newStatus
    })

    if (result.success) {
      order.value.status = newStatus
      ElMessage.success(`订单状态已更新为${statusTexts[newStatus]}`)
    } else {
      ElMessage.error(result.error || '更新订单状态失败')
    }
  } catch {
    // 用户取消
  } finally {
    updating.value = false
  }
}

const cancelOrder = async () => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '取消订单', {
      confirmButtonText: '取消订单',
      cancelButtonText: '保留订单',
      type: 'warning'
    })

    updating.value = true
    const result = await orderAPI.updateOrder(order.value.id, {
      status: 'cancelled'
    })

    if (result.success) {
      order.value.status = 'cancelled'
      ElMessage.success('订单已取消')
    } else {
      ElMessage.error(result.error || '取消订单失败')
    }
  } catch {
    // 用户取消
  } finally {
    updating.value = false
  }
}

const editOrder = () => {
  // 这里可以跳转到编辑页面或显示编辑对话框
  ElMessage.info('编辑功能开发中')
}

onMounted(() => {
  loadOrder()
})
</script>

<style scoped>
.header-tags {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.info-item.full-width {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.label {
  color: var(--text-secondary);
  font-weight: 500;
}

.value {
  color: var(--text-primary);
  font-weight: 500;
  text-align: right;
}

.info-item.full-width .value {
  text-align: left;
}

.amount {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 16px;
}

.phone-link {
  color: var(--primary-color);
  text-decoration: none;
}

.payment-actions {
  margin-bottom: 12px;
}

.item-count {
  font-size: 12px;
  color: var(--text-secondary);
}

.item-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.item-row:last-child {
  border-bottom: none;
  margin-bottom: 12px;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.item-spec {
  font-size: 12px;
  color: var(--text-secondary);
}

.item-amount {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-left: 12px;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 2px solid var(--border-color);
  font-size: 16px;
  font-weight: 600;
}

.total-label {
  color: var(--text-primary);
}

.total-amount {
  color: var(--primary-color);
}

.notes-content {
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.5;
  white-space: pre-wrap;
}

.action-section {
  margin-top: 24px;
}

.button-group {
  display: flex;
  gap: 12px;
}
</style>
