<template>
  <div class="page">
    <!-- 日历头部 -->
    <div class="calendar-header">
      <div class="month-selector">
        <el-button 
          type="text" 
          @click="previousMonth"
          :icon="ArrowLeft"
        />
        <div class="month-title">
          {{ currentYear }}年{{ currentMonth }}月
        </div>
        <el-button 
          type="text" 
          @click="nextMonth"
          :icon="ArrowRight"
        />
      </div>
      <div class="header-actions">
        <el-button 
          type="text" 
          @click="goToToday"
          size="small"
        >
          今天
        </el-button>
      </div>
    </div>

    <div class="page-content">
      <!-- 星期标题 -->
      <div class="weekdays">
        <div 
          v-for="day in weekdays"
          :key="day"
          class="weekday"
        >
          {{ day }}
        </div>
      </div>

      <!-- 日历网格 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span style="margin-left: 8px;">加载中...</span>
      </div>

      <div v-else class="calendar-grid">
        <div 
          v-for="date in calendarDates"
          :key="date.dateString"
          class="calendar-cell"
          :class="{
            'other-month': !date.isCurrentMonth,
            'today': date.isToday,
            'has-orders': date.orderCount > 0
          }"
          @click="selectDate(date)"
        >
          <div class="date-number">{{ date.day }}</div>
          
          <!-- 农历信息 -->
          <div v-if="date.lunar && date.isCurrentMonth" class="lunar-info">
            {{ date.lunar }}
          </div>
          
          <!-- 节假日信息 -->
          <div v-if="date.holiday && date.isCurrentMonth" class="holiday-info">
            {{ date.holiday }}
          </div>
          
          <!-- 订单数量 -->
          <div v-if="date.orderCount > 0 && date.isCurrentMonth" class="order-badge">
            {{ date.orderCount }}
          </div>
        </div>
      </div>

      <!-- 选中日期的详情 -->
      <div v-if="selectedDate" class="date-detail card">
        <div class="card-header">
          <span>{{ formatSelectedDate(selectedDate) }}</span>
          <el-button 
            type="primary" 
            size="small"
            @click="createOrder"
          >
            新建订单
          </el-button>
        </div>
        <div class="card-body">
          <!-- 当日订单 -->
          <div v-if="selectedDateOrders.length > 0" class="orders-section">
            <div class="section-title">当日订单 ({{ selectedDateOrders.length }})</div>
            <div class="order-list">
              <div 
                v-for="order in selectedDateOrders"
                :key="order.id"
                class="order-item touch-feedback"
                @click="viewOrder(order.id)"
              >
                <div class="order-info">
                  <div class="customer-name">{{ order.customer?.name || '未知客户' }}</div>
                  <div class="order-amount">¥{{ order.total_price }}</div>
                </div>
                <el-tag
                  :type="getStatusType(order.order_status)"
                  size="small"
                >
                  {{ getStatusText(order.order_status) }}
                </el-tag>
              </div>
            </div>
          </div>
          
          <div v-else class="empty-orders">
            <el-icon><Document /></el-icon>
            <span>当日暂无订单</span>
          </div>

          <!-- 日历信息 -->
          <div v-if="selectedDateInfo" class="calendar-info">
            <div v-if="selectedDateInfo.lunar" class="info-item">
              <span class="info-label">农历:</span>
              <span class="info-value">{{ selectedDateInfo.lunar }}</span>
            </div>
            <div v-if="selectedDateInfo.holiday" class="info-item">
              <span class="info-label">节日:</span>
              <span class="info-value">{{ selectedDateInfo.holiday }}</span>
            </div>
            <div v-if="selectedDateInfo.almanac" class="info-item">
              <span class="info-label">宜忌:</span>
              <span class="info-value">{{ selectedDateInfo.almanac }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft, 
  ArrowRight, 
  Document, 
  Loading 
} from '@element-plus/icons-vue'
import { orderAPI, calendarAPI } from '@/utils/api'

const router = useRouter()
const loading = ref(false)
const currentDate = ref(new Date())
const selectedDate = ref(null)
const selectedDateOrders = ref([])
const selectedDateInfo = ref(null)
const monthOrders = ref([])
const calendarInfo = ref({})

const weekdays = ['日', '一', '二', '三', '四', '五', '六']

const currentYear = computed(() => currentDate.value.getFullYear())
const currentMonth = computed(() => currentDate.value.getMonth() + 1)

const calendarDates = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  
  // 获取当月第一天和最后一天
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  
  // 获取日历显示的第一天（可能是上个月的日期）
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())
  
  // 获取日历显示的最后一天（可能是下个月的日期）
  const endDate = new Date(lastDay)
  endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()))
  
  const dates = []
  const current = new Date(startDate)
  const today = new Date()
  
  while (current <= endDate) {
    const dateString = current.toISOString().split('T')[0]
    const isCurrentMonth = current.getMonth() === month
    const isToday = current.toDateString() === today.toDateString()
    
    // 获取当日订单数量
    const dayOrders = monthOrders.value.filter(order =>
      order.delivery_datetime && order.delivery_datetime.startsWith(dateString)
    )
    
    // 获取日历信息
    const dayInfo = calendarInfo.value[dateString] || {}
    
    dates.push({
      date: new Date(current),
      dateString,
      day: current.getDate(),
      isCurrentMonth,
      isToday,
      orderCount: dayOrders.length,
      lunar: dayInfo.lunar,
      holiday: dayInfo.holiday,
      almanac: dayInfo.almanac
    })
    
    current.setDate(current.getDate() + 1)
  }
  
  return dates
})

const formatSelectedDate = (date) => {
  return date.date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'production': 'primary',
    'delivery': 'info',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

const previousMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  currentDate.value = newDate
  selectedDate.value = null
}

const nextMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  currentDate.value = newDate
  selectedDate.value = null
}

const goToToday = () => {
  currentDate.value = new Date()
  selectedDate.value = null
}

const selectDate = async (date) => {
  if (!date.isCurrentMonth) return
  
  selectedDate.value = date
  selectedDateInfo.value = {
    lunar: date.lunar,
    holiday: date.holiday,
    almanac: date.almanac
  }
  
  // 加载当日订单
  const dayOrders = monthOrders.value.filter(order =>
    order.delivery_datetime && order.delivery_datetime.startsWith(date.dateString)
  )
  selectedDateOrders.value = dayOrders
}

const loadMonthData = async () => {
  loading.value = true
  try {
    const year = currentDate.value.getFullYear()
    const month = currentDate.value.getMonth() + 1

    // 并行加载订单和日历信息
    const [ordersResult, monthlyResult, calendarResult] = await Promise.all([
      orderAPI.getOrders({
        limit: 1000
      }),
      orderAPI.getMonthlySummary(year, month),
      calendarAPI.getMonthCalendar(year, month)
    ])

    if (ordersResult.success) {
      // 过滤当月的订单
      const allOrders = ordersResult.data || []
      monthOrders.value = allOrders.filter(order => {
        if (!order.delivery_datetime) return false
        const orderDate = new Date(order.delivery_datetime)
        return orderDate.getFullYear() === year && orderDate.getMonth() === month - 1
      })
    }

    if (calendarResult.success) {
      calendarInfo.value = calendarResult.data || {}
    }

    // 可以使用月度汇总数据来优化显示
    if (monthlyResult.success) {
      console.log('月度汇总数据:', monthlyResult.data)
    }
  } catch (error) {
    console.error('加载日历数据失败:', error)
    ElMessage.error('加载日历数据失败')
  } finally {
    loading.value = false
  }
}

const createOrder = () => {
  router.push('/orders')
}

const viewOrder = (orderId) => {
  router.push(`/orders/${orderId}`)
}

// 监听月份变化
watch(currentDate, () => {
  loadMonthData()
}, { immediate: true })

onMounted(() => {
  // 默认选择今天
  const today = new Date()
  if (today.getMonth() === currentDate.value.getMonth() && 
      today.getFullYear() === currentDate.value.getFullYear()) {
    setTimeout(() => {
      const todayDate = calendarDates.value.find(date => date.isToday)
      if (todayDate) {
        selectDate(todayDate)
      }
    }, 100)
  }
})
</script>

<style scoped>
.calendar-header {
  background: var(--bg-color);
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.month-selector {
  display: flex;
  align-items: center;
  gap: 16px;
}

.month-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  min-width: 120px;
  text-align: center;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: var(--bg-color);
  border-bottom: 1px solid var(--border-color);
}

.weekday {
  padding: 12px 4px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: var(--border-color);
}

.calendar-cell {
  background: var(--bg-color);
  min-height: 80px;
  padding: 8px 4px;
  cursor: pointer;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: background-color 0.2s ease;
}

.calendar-cell:hover {
  background: var(--bg-page);
}

.calendar-cell.other-month {
  background: #fafafa;
  color: var(--text-secondary);
}

.calendar-cell.today {
  background: #e6f7ff;
}

.calendar-cell.has-orders {
  background: #f6ffed;
}

.date-number {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.other-month .date-number {
  color: var(--text-secondary);
}

.today .date-number {
  color: var(--primary-color);
}

.lunar-info {
  font-size: 10px;
  color: var(--text-secondary);
  text-align: center;
  line-height: 1.2;
}

.holiday-info {
  font-size: 10px;
  color: var(--danger-color);
  text-align: center;
  font-weight: 500;
}

.order-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
}

.date-detail {
  margin-top: 16px;
}

.orders-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  cursor: pointer;
}

.order-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.customer-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.order-amount {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
}

.empty-orders {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: var(--text-secondary);
  font-size: 14px;
}

.calendar-info {
  border-top: 1px solid var(--border-color);
  padding-top: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.info-value {
  color: var(--text-primary);
}
</style>
