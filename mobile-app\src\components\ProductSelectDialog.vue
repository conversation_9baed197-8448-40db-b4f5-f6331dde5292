<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="选择商品"
    width="90%"
  >
    <div class="product-select">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索商品名称"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 快速创建按钮 -->
      <div class="quick-actions">
        <el-button 
          type="primary" 
          @click="showCreateForm = true"
          style="width: 100%"
        >
          <el-icon><Plus /></el-icon>
          新建商品
        </el-button>
      </div>

      <!-- 商品列表 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span style="margin-left: 8px;">加载中...</span>
      </div>

      <div v-else-if="filteredProducts.length === 0" class="empty-state">
        <el-icon><Goods /></el-icon>
        <div class="empty-state-text">
          {{ searchQuery ? '未找到匹配的商品' : '暂无商品' }}
        </div>
      </div>

      <div v-else class="product-list">
        <div 
          v-for="product in filteredProducts"
          :key="product.id"
          class="product-item touch-feedback"
          @click="selectProduct(product)"
        >
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-details">
              <span class="product-price">¥{{ product.price }}</span>
              <span class="product-unit">/ {{ product.unit }}</span>
              <span v-if="product.category" class="product-category">
                {{ product.category }}
              </span>
            </div>
            <div v-if="product.description" class="product-description">
              {{ product.description }}
            </div>
          </div>
          <el-icon class="select-icon"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <!-- 创建商品表单 -->
    <el-dialog
      v-model="showCreateForm"
      title="新建商品"
      width="90%"
      append-to-body
    >
      <ProductCreateForm 
        @success="handleCreateSuccess"
        @cancel="showCreateForm = false"
      />
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Plus, 
  Goods, 
  Loading,
  ArrowRight
} from '@element-plus/icons-vue'
import { productAPI } from '@/utils/api'
import ProductCreateForm from './ProductCreateForm.vue'

const props = defineProps({
  modelValue: Boolean
})

const emit = defineEmits(['update:modelValue', 'select'])

const loading = ref(false)
const products = ref([])
const searchQuery = ref('')
const showCreateForm = ref(false)

const filteredProducts = computed(() => {
  if (!searchQuery.value.trim()) {
    return products.value
  }
  
  const query = searchQuery.value.trim().toLowerCase()
  return products.value.filter(product => 
    product.name.toLowerCase().includes(query) ||
    (product.category && product.category.toLowerCase().includes(query))
  )
})

const loadProducts = async () => {
  loading.value = true
  try {
    const result = await productAPI.getProducts()
    if (result.success) {
      products.value = result.data || []
    } else {
      ElMessage.error(result.error || '加载商品失败')
    }
  } catch (error) {
    ElMessage.error('加载商品失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索是通过计算属性实现的，这里不需要额外操作
}

const selectProduct = (product) => {
  emit('select', product)
  emit('update:modelValue', false)
}

const handleCreateSuccess = (newProduct) => {
  showCreateForm.value = false
  products.value.unshift(newProduct)
  ElMessage.success('商品创建成功')
  
  // 自动选择新创建的商品
  selectProduct(newProduct)
}

// 监听对话框打开状态
watch(() => props.modelValue, (newValue) => {
  if (newValue && products.value.length === 0) {
    loadProducts()
  }
})

onMounted(() => {
  if (props.modelValue) {
    loadProducts()
  }
})
</script>

<style scoped>
.product-select {
  max-height: 60vh;
  display: flex;
  flex-direction: column;
}

.search-bar {
  margin-bottom: 16px;
}

.quick-actions {
  margin-bottom: 16px;
}

.product-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.product-item:hover {
  border-color: var(--primary-color);
  background: rgba(64, 158, 255, 0.05);
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 6px;
}

.product-details {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.product-price {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-color);
}

.product-unit {
  font-size: 14px;
  color: var(--text-secondary);
}

.product-category {
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--bg-page);
  padding: 2px 6px;
  border-radius: 4px;
}

.product-description {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-top: 4px;
}

.select-icon {
  color: var(--text-secondary);
  margin-left: 12px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--text-secondary);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.empty-state .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state-text {
  font-size: 14px;
}
</style>
