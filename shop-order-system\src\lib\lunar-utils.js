// 农历计算工具函数
import { Lunar, Solar } from 'lunar-javascript'

/**
 * 公历转农历
 * @param {Date|string} date - 公历日期
 * @returns {Object} 农历信息
 */
export function solarToLunar(date) {
  try {
    const solar = typeof date === 'string' ? Solar.fromYmd(
      parseInt(date.split('-')[0]),
      parseInt(date.split('-')[1]),
      parseInt(date.split('-')[2])
    ) : Solar.fromDate(date)
    
    const lunar = solar.getLunar()
    
    return {
      year: lunar.getYear(),
      month: lunar.getMonth(),
      day: lunar.getDay(),
      yearCn: lunar.getYearInChinese(),
      monthCn: lunar.getMonthInChinese(),
      dayCn: lunar.getDayInChinese(),
      yearGanZhi: lunar.getYearInGanZhi(),
      monthGanZhi: lunar.getMonthInGanZhi(),
      dayGanZhi: lunar.getDayInGan<PERSON><PERSON>(),
      timeGanZhi: lunar.getTimeInGan<PERSON><PERSON>(),
      zodiac: lunar.getYearShengXiao(),
      festivals: lunar.getFestivals(),
      jieQi: lunar.getJieQi(),
      isLeapMonth: typeof lunar.isLeap === 'function' ? lunar.isLeap() : false,
      weekDay: solar.getWeek(),
      weekDayCn: solar.getWeekInChinese(),
      constellation: solar.getXingZuo(),
      lunarDateStr: `${lunar.getMonthInChinese()}${lunar.getDayInChinese()}`,
      fullLunarStr: `${lunar.getYearInChinese()}年${lunar.getMonthInChinese()}${lunar.getDayInChinese()}`,
      // 黄历宜忌信息
      yi: (typeof lunar.getDayYi === 'function' ? lunar.getDayYi() : []) || [],  // 宜做的事情
      ji: (typeof lunar.getDayJi === 'function' ? lunar.getDayJi() : []) || [],  // 忌做的事情
      zhiXing: (typeof lunar.getZhiXing === 'function' ? lunar.getZhiXing() : '') || '', // 建除十二值星
      pengZuBaiJi: ((typeof lunar.getPengZuGan === 'function' ? lunar.getPengZuGan() : '') + ' ' + (typeof lunar.getPengZuZhi === 'function' ? lunar.getPengZuZhi() : '')).trim(), // 彭祖百忌
      chong: (typeof lunar.getDayChongDesc === 'function' ? lunar.getDayChongDesc() : '') || '', // 冲煞
      sha: (typeof lunar.getDaySha === 'function' ? lunar.getDaySha() : '') || '', // 煞方
      // 吉神方位 - 简化处理，避免复杂的API调用
      xiShen: (typeof lunar.getDayPositionXiDesc === 'function' ? lunar.getDayPositionXiDesc() : '') || '', // 喜神方位
      fuShen: (typeof lunar.getDayPositionFuDesc === 'function' ? lunar.getDayPositionFuDesc() : '') || '', // 福神方位
      caiShen: (typeof lunar.getDayPositionCaiDesc === 'function' ? lunar.getDayPositionCaiDesc() : '') || '', // 财神方位
      // 星宿信息 - 简化处理
      xingXiu: ((typeof lunar.getXiu === 'function' ? lunar.getXiu() : '') + (typeof lunar.getZheng === 'function' ? lunar.getZheng() : '') + (typeof lunar.getAnimal === 'function' ? lunar.getAnimal() : '')).trim(), // 星宿
      // 纳音 - 简化处理
      naYin: [
        (typeof lunar.getYearNaYin === 'function' ? lunar.getYearNaYin() : ''),
        (typeof lunar.getMonthNaYin === 'function' ? lunar.getMonthNaYin() : ''),
        (typeof lunar.getDayNaYin === 'function' ? lunar.getDayNaYin() : ''),
        (typeof lunar.getTimeNaYin === 'function' ? lunar.getTimeNaYin() : '')
      ].filter(Boolean).join(' ')
    }
  } catch (error) {
    console.error('农历转换失败:', error)
    return {
      year: 0,
      month: 0,
      day: 0,
      yearCn: '',
      monthCn: '',
      dayCn: '',
      yearGanZhi: '',
      monthGanZhi: '',
      dayGanZhi: '',
      timeGanZhi: '',
      zodiac: '',
      festivals: [],
      jieQi: '',
      isLeapMonth: false,
      weekDay: 0,
      weekDayCn: '',
      constellation: '',
      lunarDateStr: '',
      fullLunarStr: '',
      // 黄历宜忌信息默认值
      yi: [],
      ji: [],
      zhiXing: '',
      pengZuBaiJi: '',
      chong: '',
      sha: '',
      xiShen: '',
      fuShen: '',
      caiShen: '',
      xingXiu: '',
      naYin: ''
    }
  }
}

/**
 * 农历转公历
 * @param {number} year - 农历年
 * @param {number} month - 农历月
 * @param {number} day - 农历日
 * @param {boolean} isLeapMonth - 是否闰月
 * @returns {Object} 公历信息
 */
export function lunarToSolar(year, month, day, isLeapMonth = false) {
  try {
    const lunar = Lunar.fromYmd(year, month, day, isLeapMonth)
    const solar = lunar.getSolar()
    
    return {
      year: solar.getYear(),
      month: solar.getMonth(),
      day: solar.getDay(),
      date: new Date(solar.getYear(), solar.getMonth() - 1, solar.getDay()),
      dateStr: `${solar.getYear()}-${solar.getMonth().toString().padStart(2, '0')}-${solar.getDay().toString().padStart(2, '0')}`,
      weekDay: solar.getWeek(),
      weekDayCn: solar.getWeekInChinese(),
      constellation: solar.getXingZuo()
    }
  } catch (error) {
    console.error('公历转换失败:', error)
    return {
      year: 0,
      month: 0,
      day: 0,
      date: null,
      dateStr: '',
      weekDay: 0,
      weekDayCn: '',
      constellation: ''
    }
  }
}

/**
 * 获取指定月份的农历信息
 * @param {number} year - 年份
 * @param {number} month - 月份
 * @returns {Array} 月份农历信息数组
 */
export function getMonthLunarInfo(year, month) {
  const monthInfo = []
  const daysInMonth = new Date(year, month, 0).getDate()
  
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month - 1, day)
    const lunarInfo = solarToLunar(date)
    monthInfo.push({
      solarDate: `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,
      solarDay: day,
      ...lunarInfo
    })
  }
  
  return monthInfo
}

/**
 * 获取农历节日
 * @param {Date|string} date - 日期
 * @returns {Array} 节日数组
 */
export function getLunarFestivals(date) {
  const lunarInfo = solarToLunar(date)
  return lunarInfo.festivals || []
}

/**
 * 获取节气信息
 * @param {Date|string} date - 日期
 * @returns {string} 节气名称
 */
export function getJieQi(date) {
  const lunarInfo = solarToLunar(date)
  return lunarInfo.jieQi || ''
}

/**
 * 判断是否为农历节日
 * @param {Date|string} date - 日期
 * @returns {boolean} 是否为节日
 */
export function isLunarFestival(date) {
  const festivals = getLunarFestivals(date)
  return festivals.length > 0
}

/**
 * 获取生肖
 * @param {number} year - 年份
 * @returns {string} 生肖
 */
export function getZodiac(year) {
  try {
    const lunar = Lunar.fromDate(new Date(year, 0, 1))
    return lunar.getYearShengXiao()
  } catch (error) {
    console.error('获取生肖失败:', error)
    return ''
  }
}

/**
 * 获取星座
 * @param {Date|string} date - 日期
 * @returns {string} 星座
 */
export function getConstellation(date) {
  try {
    const solar = typeof date === 'string' ? Solar.fromYmd(
      parseInt(date.split('-')[0]),
      parseInt(date.split('-')[1]),
      parseInt(date.split('-')[2])
    ) : Solar.fromDate(date)
    
    return solar.getXingZuo()
  } catch (error) {
    console.error('获取星座失败:', error)
    return ''
  }
}

/**
 * 格式化农历日期显示
 * @param {Object} lunarInfo - 农历信息
 * @returns {string} 格式化的农历日期
 */
export function formatLunarDate(lunarInfo) {
  if (!lunarInfo || !lunarInfo.monthCn || !lunarInfo.dayCn) {
    return ''
  }
  
  // 如果是初一，显示月份
  if (lunarInfo.dayCn === '初一') {
    return lunarInfo.monthCn
  }
  
  // 如果有节气，优先显示节气
  if (lunarInfo.jieQi) {
    return lunarInfo.jieQi
  }
  
  // 如果有节日，优先显示节日
  if (lunarInfo.festivals && lunarInfo.festivals.length > 0) {
    return lunarInfo.festivals[0]
  }
  
  // 否则显示农历日期
  return lunarInfo.dayCn
}

/**
 * 获取农历月份天数
 * @param {number} year - 农历年
 * @param {number} month - 农历月
 * @returns {number} 天数
 */
export function getLunarMonthDays(year, month) {
  try {
    const lunar = Lunar.fromYmd(year, month, 1)
    return lunar.getDayCount()
  } catch (error) {
    console.error('获取农历月份天数失败:', error)
    return 30
  }
}

/**
 * 判断是否为闰月
 * @param {number} year - 农历年
 * @param {number} month - 农历月
 * @returns {boolean} 是否为闰月
 */
export function isLeapMonth(year, month) {
  try {
    const lunar = Lunar.fromYmd(year, month, 1)
    return typeof lunar.isLeap === 'function' ? lunar.isLeap() : false
  } catch (error) {
    console.error('判断闰月失败:', error)
    return false
  }
}

/**
 * 获取黄历宜忌信息
 * @param {Date|string} date - 日期
 * @returns {Object} 宜忌信息
 */
export function getHuangLiInfo(date) {
  try {
    const lunarInfo = solarToLunar(date)
    return {
      yi: lunarInfo.yi || [],
      ji: lunarInfo.ji || [],
      zhiXing: lunarInfo.zhiXing || '',
      pengZuBaiJi: lunarInfo.pengZuBaiJi || '',
      chong: lunarInfo.chong || '',
      sha: lunarInfo.sha || '',
      xiShen: lunarInfo.xiShen || '',
      fuShen: lunarInfo.fuShen || '',
      caiShen: lunarInfo.caiShen || '',
      yangGuiShen: lunarInfo.yangGuiShen || '',
      yinGuiShen: lunarInfo.yinGuiShen || '',
      xingXiu: lunarInfo.xingXiu || '',
      naYin: lunarInfo.naYin || ''
    }
  } catch (error) {
    console.error('获取黄历信息失败:', error)
    return {
      yi: [],
      ji: [],
      zhiXing: '',
      pengZuBaiJi: '',
      chong: '',
      sha: '',
      xiShen: '',
      fuShen: '',
      caiShen: '',
      yangGuiShen: '',
      yinGuiShen: '',
      xingXiu: '',
      naYin: ''
    }
  }
}

/**
 * 获取简化的黄历信息（用于日历格子显示）
 * @param {Date|string} date - 日期
 * @returns {Object} 简化的黄历信息
 */
export function getSimpleHuangLiInfo(date) {
  try {
    const lunarInfo = solarToLunar(date)

    // 取前3个宜忌项目用于显示
    const yi = (lunarInfo.yi || []).slice(0, 3)
    const ji = (lunarInfo.ji || []).slice(0, 3)

    return {
      yi: yi,
      ji: ji,
      yiText: yi.length > 0 ? `宜: ${yi.join('、')}` : '',
      jiText: ji.length > 0 ? `忌: ${ji.join('、')}` : '',
      zhiXing: lunarInfo.zhiXing || '',
      jieQi: lunarInfo.jieQi || '',
      festivals: lunarInfo.festivals || []
    }
  } catch (error) {
    console.error('获取简化黄历信息失败:', error)
    return {
      yi: [],
      ji: [],
      yiText: '',
      jiText: '',
      zhiXing: '',
      jieQi: '',
      festivals: []
    }
  }
}

/**
 * 判断是否为黄道吉日
 * @param {Date|string} date - 日期
 * @returns {boolean} 是否为黄道吉日
 */
export function isHuangDaoJiRi(date) {
  try {
    const solar = typeof date === 'string' ? Solar.fromYmd(
      parseInt(date.split('-')[0]),
      parseInt(date.split('-')[1]),
      parseInt(date.split('-')[2])
    ) : Solar.fromDate(date)

    const lunar = solar.getLunar()

    // 直接从lunar对象获取建除十二值星
    const zhiXing = (typeof lunar.getZhiXing === 'function' ? lunar.getZhiXing() : '') || ''

    // 建除十二值星中的吉日：建、除、满、平、定、执、成、开
    const jiRiList = ['建', '除', '满', '平', '定', '执', '成', '开']
    return jiRiList.includes(zhiXing)
  } catch (error) {
    console.error('判断黄道吉日失败:', error)
    return false
  }
}

// 导出所有函数
export default {
  solarToLunar,
  lunarToSolar,
  getMonthLunarInfo,
  getLunarFestivals,
  getJieQi,
  isLunarFestival,
  getZodiac,
  getConstellation,
  formatLunarDate,
  getLunarMonthDays,
  isLeapMonth,
  getHuangLiInfo,
  getSimpleHuangLiInfo,
  isHuangDaoJiRi
}
