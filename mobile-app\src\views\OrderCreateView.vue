<template>
  <div class="page-container">
    <div class="page-header">
      <el-button 
        type="text" 
        @click="goBack"
        class="back-button"
      >
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <h1 class="page-title">新建订单</h1>
    </div>

    <div class="page-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
        class="order-form"
      >
        <!-- 客户信息 -->
        <div class="form-section">
          <div class="section-title">客户信息</div>
          
          <el-form-item label="选择客户" prop="customer_id">
            <div class="customer-select-wrapper">
              <el-select
                v-model="form.customer_id"
                placeholder="选择客户"
                style="flex: 1"
                filterable
                @change="handleCustomerChange"
              >
                <el-option
                  v-for="customer in customers"
                  :key="customer.id"
                  :label="`${customer.name} (${customer.contact})`"
                  :value="customer.id"
                />
              </el-select>
              <el-button 
                type="primary" 
                @click="showCreateCustomer = true"
                style="margin-left: 8px;"
              >
                新建
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="收货地址" prop="delivery_address">
            <el-input
              v-model="form.delivery_address"
              type="textarea"
              :rows="3"
              placeholder="请输入收货地址"
            />
          </el-form-item>

          <el-form-item label="交付时间" prop="delivery_datetime">
            <el-date-picker
              v-model="form.delivery_datetime"
              type="datetime"
              placeholder="选择交付时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </div>

        <!-- 商品信息 -->
        <div class="form-section">
          <div class="section-title">
            <span>商品清单</span>
            <el-button 
              type="primary" 
              size="small"
              @click="addOrderItem"
            >
              添加商品
            </el-button>
          </div>

          <div v-if="form.order_items.length === 0" class="empty-items">
            <el-icon><Goods /></el-icon>
            <div class="empty-text">暂无商品，请添加商品</div>
          </div>

          <div v-else class="order-items">
            <div 
              v-for="(item, index) in form.order_items"
              :key="index"
              class="order-item"
            >
              <div class="item-header">
                <span class="item-title">商品 {{ index + 1 }}</span>
                <el-button 
                  type="danger" 
                  size="small"
                  text
                  @click="removeOrderItem(index)"
                >
                  删除
                </el-button>
              </div>

              <el-form-item :prop="`order_items.${index}.product_id`" :rules="itemRules.product_id">
                <el-select
                  v-model="item.product_id"
                  placeholder="选择商品"
                  style="width: 100%"
                  @change="handleProductChange(index)"
                >
                  <el-option
                    v-for="product in products"
                    :key="product.id"
                    :label="`${product.name} - ¥${product.price}`"
                    :value="product.id"
                  />
                </el-select>
              </el-form-item>

              <div class="item-details">
                <el-form-item label="数量" :prop="`order_items.${index}.quantity`" :rules="itemRules.quantity">
                  <el-input-number
                    v-model="item.quantity"
                    :min="0.1"
                    :step="0.1"
                    :precision="1"
                    style="width: 100%"
                    @change="calculateItemTotal(index)"
                  />
                </el-form-item>

                <el-form-item label="单价" :prop="`order_items.${index}.price_at_order`" :rules="itemRules.price_at_order">
                  <el-input-number
                    v-model="item.price_at_order"
                    :min="0"
                    :step="0.01"
                    :precision="2"
                    style="width: 100%"
                    @change="calculateItemTotal(index)"
                  />
                </el-form-item>
              </div>

              <div class="item-total">
                小计: ¥{{ ((item.quantity || 0) * (item.price_at_order || 0)).toFixed(2) }}
              </div>
            </div>
          </div>

          <div class="order-total">
            <div class="total-row">
              <span class="total-label">订单总额:</span>
              <span class="total-amount">¥{{ orderTotal.toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <div class="section-title">其他信息</div>
          
          <el-form-item label="订单状态">
            <el-select v-model="form.order_status" style="width: 100%">
              <el-option label="待处理" value="pending" />
              <el-option label="生产中" value="production" />
              <el-option label="配送中" value="delivery" />
              <el-option label="已完成" value="completed" />
            </el-select>
          </el-form-item>

          <el-form-item label="付款状态">
            <el-select v-model="form.payment_status" style="width: 100%">
              <el-option label="未付款" value="unpaid" />
              <el-option label="已付款" value="paid" />
              <el-option label="部分付款" value="partial" />
            </el-select>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="form.notes"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 底部操作栏 -->
    <div class="page-footer">
      <el-button @click="goBack" style="flex: 1;">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleSubmit"
        :loading="submitting"
        style="flex: 2;"
      >
        创建订单
      </el-button>
    </div>

    <!-- 新建客户对话框 -->
    <CustomerCreateForm
      v-model="showCreateCustomer"
      @success="handleCustomerCreated"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Goods } from '@element-plus/icons-vue'
import { orderAPI, customerAPI, productAPI } from '@/utils/api'
import CustomerCreateForm from '@/components/CustomerCreateForm.vue'

const router = useRouter()
const formRef = ref()
const submitting = ref(false)
const showCreateCustomer = ref(false)

const customers = ref([])
const products = ref([])

const form = reactive({
  customer_id: '',
  delivery_address: '',
  delivery_datetime: '',
  order_status: 'pending',
  payment_status: 'unpaid',
  notes: '',
  order_items: []
})

const rules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  delivery_address: [
    { required: true, message: '请输入收货地址', trigger: 'blur' }
  ],
  delivery_datetime: [
    { required: true, message: '请选择交付时间', trigger: 'change' }
  ]
}

const itemRules = {
  product_id: [
    { required: true, message: '请选择商品', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 0.1, message: '数量必须大于0', trigger: 'blur' }
  ],
  price_at_order: [
    { required: true, message: '请输入单价', trigger: 'blur' },
    { type: 'number', min: 0, message: '单价不能为负数', trigger: 'blur' }
  ]
}

const orderTotal = computed(() => {
  return form.order_items.reduce((total, item) => {
    return total + ((item.quantity || 0) * (item.price_at_order || 0))
  }, 0)
})

const goBack = () => {
  router.back()
}

const loadCustomers = async () => {
  try {
    const result = await customerAPI.getCustomers()
    if (result.success) {
      customers.value = result.data || []
    }
  } catch (error) {
    console.error('加载客户列表失败:', error)
  }
}

const loadProducts = async () => {
  try {
    const result = await productAPI.getProducts()
    if (result.success) {
      products.value = result.data || []
    }
  } catch (error) {
    console.error('加载商品列表失败:', error)
  }
}

const handleCustomerChange = (customerId) => {
  const customer = customers.value.find(c => c.id === customerId)
  if (customer) {
    // 可以自动填充地址等信息
    // form.delivery_address = customer.address || ''
  }
}

const handleProductChange = (index) => {
  const item = form.order_items[index]
  const product = products.value.find(p => p.id === item.product_id)
  if (product) {
    item.price_at_order = product.price
    calculateItemTotal(index)
  }
}

const addOrderItem = () => {
  form.order_items.push({
    product_id: '',
    quantity: 1,
    price_at_order: 0
  })
}

const removeOrderItem = (index) => {
  form.order_items.splice(index, 1)
}

const calculateItemTotal = (index) => {
  // 触发响应式更新
  const item = form.order_items[index]
  item.total = (item.quantity || 0) * (item.price_at_order || 0)
}

const handleCustomerCreated = (customer) => {
  customers.value.push(customer)
  form.customer_id = customer.id
  ElMessage.success('客户创建成功')
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    if (form.order_items.length === 0) {
      ElMessage.error('请至少添加一个商品')
      return
    }

    submitting.value = true
    
    const orderData = {
      ...form,
      total_price: orderTotal.value
    }
    
    const result = await orderAPI.createOrder(orderData)
    
    if (result.success) {
      ElMessage.success('订单创建成功')
      router.push(`/orders/${result.data.id}`)
    } else {
      ElMessage.error(result.error || '创建订单失败')
    }
  } catch (error) {
    ElMessage.error('创建订单失败')
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  loadCustomers()
  loadProducts()
  
  // 默认添加一个商品项
  addOrderItem()
})
</script>

<style scoped>
.order-form {
  padding-bottom: 80px; /* 为底部操作栏留出空间 */
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--bg-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.customer-select-wrapper {
  display: flex;
  align-items: center;
}

.empty-items {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.empty-items .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
}

.order-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.order-item {
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-page);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.item-title {
  font-weight: 500;
  color: var(--text-primary);
}

.item-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 8px;
}

.item-total {
  text-align: right;
  font-weight: 600;
  color: var(--primary-color);
}

.order-total {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 2px solid var(--border-color);
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-label {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.total-amount {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
}

.page-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: var(--bg-color);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 12px;
  z-index: 100;
}

/* 安全区域适配 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .page-footer {
    padding-bottom: calc(16px + env(safe-area-inset-bottom));
  }
}
</style>
