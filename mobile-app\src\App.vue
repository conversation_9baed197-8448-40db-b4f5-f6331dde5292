<template>
  <div id="app">
    <!-- 顶部导航栏 -->
    <AppHeader v-if="!hideHeader" />
    
    <!-- 主要内容区域 -->
    <router-view v-slot="{ Component, route }">
      <keep-alive>
        <component 
          :is="Component" 
          v-if="route.meta.keepAlive"
          :key="route.fullPath"
        />
      </keep-alive>
      <component 
        :is="Component" 
        v-if="!route.meta.keepAlive"
        :key="route.fullPath"
      />
    </router-view>
    
    <!-- 底部导航栏 -->
    <AppTabbar v-if="!hideTabbar" />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import AppHeader from '@/components/AppHeader.vue'
import AppTabbar from '@/components/AppTabbar.vue'

const route = useRoute()

const hideHeader = computed(() => route.meta.hideHeader)
const hideTabbar = computed(() => route.meta.hideTabbar)
</script>

<style>
/* App级别的样式已在mobile.css中定义 */
</style>
