<template>
  <div class="login-page">
    <div class="login-container">
      <!-- Logo和标题 -->
      <div class="login-header">
        <div class="logo">
          <el-icon :size="48"><Shop /></el-icon>
        </div>
        <h1 class="title">小商店订单系统</h1>
        <p class="subtitle">移动端管理平台</p>
      </div>

      <!-- 登录表单 -->
      <div class="login-form">
        <el-form
          ref="formRef"
          :model="loginForm"
          :rules="loginRules"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="email">
            <el-input
              v-model="loginForm.email"
              placeholder="请输入邮箱"
              size="large"
              :prefix-icon="Message"
              clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              :prefix-icon="Lock"
              show-password
              @keyup.enter="handleLogin"
            />
          </el-form-item>

          <el-form-item>
            <el-checkbox v-model="rememberMe">
              记住我
            </el-checkbox>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              @click="handleLogin"
              style="width: 100%"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 其他选项 -->
      <div class="login-footer">
        <div class="links">
          <a href="#" @click.prevent="showForgotPassword">忘记密码？</a>
        </div>
        
        <div class="demo-info">
          <el-divider>演示账号</el-divider>
          <div class="demo-accounts">
            <div class="demo-account" @click="fillDemoAccount('admin')">
              <div class="demo-label">管理员</div>
              <div class="demo-email"><EMAIL></div>
            </div>
            <div class="demo-account" @click="fillDemoAccount('user')">
              <div class="demo-label">普通用户</div>
              <div class="demo-email"><EMAIL></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 忘记密码对话框 -->
    <el-dialog
      v-model="showForgotDialog"
      title="忘记密码"
      width="90%"
    >
      <el-form @submit.prevent="handleForgotPassword">
        <el-form-item label="邮箱地址">
          <el-input
            v-model="forgotEmail"
            placeholder="请输入注册邮箱"
            :prefix-icon="Message"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showForgotDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleForgotPassword"
            :loading="forgotLoading"
          >
            发送重置邮件
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Shop, 
  Message, 
  Lock 
} from '@element-plus/icons-vue'
import { authAPI, setAuthToken } from '@/utils/api'

const router = useRouter()
const formRef = ref()
const loading = ref(false)
const forgotLoading = ref(false)
const showForgotDialog = ref(false)

const loginForm = ref({
  email: '',
  password: ''
})

const forgotEmail = ref('')
const rememberMe = ref(false)

const loginRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
  } catch {
    return
  }

  loading.value = true
  try {
    const result = await authAPI.login(loginForm.value.email, loginForm.value.password)
    
    if (result.success) {
      // 保存token
      setAuthToken(result.data.token)
      
      // 如果选择记住我，保存到localStorage
      if (rememberMe.value) {
        localStorage.setItem('remember_email', loginForm.value.email)
      } else {
        localStorage.removeItem('remember_email')
      }
      
      ElMessage.success('登录成功')
      
      // 跳转到首页
      router.push('/')
    } else {
      ElMessage.error(result.error || '登录失败')
    }
  } catch (error) {
    ElMessage.error('登录失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

const fillDemoAccount = (type) => {
  if (type === 'admin') {
    loginForm.value.email = '<EMAIL>'
    loginForm.value.password = 'admin123'
  } else {
    loginForm.value.email = '<EMAIL>'
    loginForm.value.password = 'admin123'
  }
}

const showForgotPassword = () => {
  forgotEmail.value = loginForm.value.email
  showForgotDialog.value = true
}

const handleForgotPassword = async () => {
  if (!forgotEmail.value) {
    ElMessage.warning('请输入邮箱地址')
    return
  }

  forgotLoading.value = true
  try {
    // 这里应该调用忘记密码API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    
    ElMessage.success('重置邮件已发送，请查收邮箱')
    showForgotDialog.value = false
  } catch (error) {
    ElMessage.error('发送失败，请稍后重试')
  } finally {
    forgotLoading.value = false
  }
}

onMounted(() => {
  // 检查是否有记住的邮箱
  const rememberedEmail = localStorage.getItem('remember_email')
  if (rememberedEmail) {
    loginForm.value.email = rememberedEmail
    rememberMe.value = true
  }
  
  // 检查是否已经登录
  const token = localStorage.getItem('auth_token')
  if (token) {
    router.push('/')
  }
})
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  background: var(--bg-color);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px 32px;
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  color: var(--primary-color);
  margin-bottom: 16px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.subtitle {
  font-size: 14px;
  color: var(--text-secondary);
}

.login-form {
  margin-bottom: 24px;
}

.login-footer {
  text-align: center;
}

.links {
  margin-bottom: 24px;
}

.links a {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 14px;
}

.links a:hover {
  text-decoration: underline;
}

.demo-info {
  margin-top: 24px;
}

.demo-accounts {
  display: flex;
  gap: 12px;
  margin-top: 12px;
}

.demo-account {
  flex: 1;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.demo-account:hover {
  border-color: var(--primary-color);
  background: rgba(64, 158, 255, 0.05);
}

.demo-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.demo-email {
  font-size: 11px;
  color: var(--text-secondary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单样式优化 */
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input--large .el-input__wrapper) {
  padding: 12px 16px;
  border-radius: 8px;
}

:deep(.el-button--large) {
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 600;
}

:deep(.el-checkbox) {
  color: var(--text-secondary);
}

:deep(.el-divider__text) {
  color: var(--text-secondary);
  font-size: 12px;
}
</style>
