<template>
  <el-dialog
    v-model="visible"
    title="编辑订单"
    width="90%"
    :before-close="handleClose"
    class="mobile-dialog"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
      class="order-edit-form"
    >
      <el-form-item label="客户" prop="customer_id">
        <el-select
          v-model="form.customer_id"
          placeholder="选择客户"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="customer in customers"
            :key="customer.id"
            :label="customer.name"
            :value="customer.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="收货地址" prop="delivery_address">
        <el-input
          v-model="form.delivery_address"
          type="textarea"
          :rows="3"
          placeholder="请输入收货地址"
        />
      </el-form-item>

      <el-form-item label="交付时间" prop="delivery_datetime">
        <el-date-picker
          v-model="form.delivery_datetime"
          type="datetime"
          placeholder="选择交付时间"
          style="width: 100%"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="订单状态" prop="order_status">
        <el-select
          v-model="form.order_status"
          placeholder="选择订单状态"
          style="width: 100%"
        >
          <el-option label="待处理" value="pending" />
          <el-option label="生产中" value="production" />
          <el-option label="配送中" value="delivery" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
      </el-form-item>

      <el-form-item label="付款状态" prop="payment_status">
        <el-select
          v-model="form.payment_status"
          placeholder="选择付款状态"
          style="width: 100%"
        >
          <el-option label="未付款" value="unpaid" />
          <el-option label="已付款" value="paid" />
          <el-option label="部分付款" value="partial" />
        </el-select>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { orderAPI, customerAPI } from '@/utils/api'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  order: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const submitting = ref(false)
const customers = ref([])

const visible = ref(props.modelValue)

const form = reactive({
  customer_id: '',
  delivery_address: '',
  delivery_datetime: '',
  order_status: '',
  payment_status: '',
  notes: ''
})

const rules = {
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  delivery_address: [
    { required: true, message: '请输入收货地址', trigger: 'blur' }
  ],
  delivery_datetime: [
    { required: true, message: '请选择交付时间', trigger: 'change' }
  ],
  order_status: [
    { required: true, message: '请选择订单状态', trigger: 'change' }
  ],
  payment_status: [
    { required: true, message: '请选择付款状态', trigger: 'change' }
  ]
}

const loadCustomers = async () => {
  try {
    const result = await customerAPI.getCustomers()
    if (result.success) {
      customers.value = result.data || []
    }
  } catch (error) {
    console.error('加载客户列表失败:', error)
  }
}

const initForm = () => {
  if (props.order && props.order.id) {
    form.customer_id = props.order.customer_id
    form.delivery_address = props.order.delivery_address
    form.delivery_datetime = props.order.delivery_datetime
    form.order_status = props.order.order_status
    form.payment_status = props.order.payment_status
    form.notes = props.order.notes || ''
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true
    const result = await orderAPI.updateOrder(props.order.id, form)
    
    if (result.success) {
      ElMessage.success('订单更新成功')
      emit('success', result.data)
      handleClose()
    } else {
      ElMessage.error(result.error || '更新订单失败')
    }
  } catch (error) {
    ElMessage.error('更新订单失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  visible.value = false
  emit('update:modelValue', false)
}

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    initForm()
    loadCustomers()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

onMounted(() => {
  if (visible.value) {
    initForm()
    loadCustomers()
  }
})
</script>

<style scoped>
.order-edit-form {
  padding: 0 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.mobile-dialog {
  margin: 5vh auto;
}

@media (max-width: 768px) {
  .mobile-dialog {
    margin: 0;
    width: 100% !important;
    height: 100vh;
    max-height: none;
  }
  
  .mobile-dialog :deep(.el-dialog) {
    border-radius: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }
  
  .mobile-dialog :deep(.el-dialog__body) {
    flex: 1;
    overflow-y: auto;
  }
}
</style>
