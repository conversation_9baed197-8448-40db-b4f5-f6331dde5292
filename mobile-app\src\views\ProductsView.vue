<template>
  <div class="page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <el-input
        v-model="searchQuery"
        placeholder="搜索商品名称"
        clearable
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <div class="page-content">
      <!-- 新建商品按钮 -->
      <div class="action-section">
        <el-button 
          type="primary" 
          @click="showCreateDialog = true"
          style="width: 100%"
        >
          <el-icon><Plus /></el-icon>
          新建商品
        </el-button>
      </div>

      <!-- 商品列表 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span style="margin-left: 8px;">加载中...</span>
      </div>
      
      <div v-else-if="products.length === 0" class="empty-state">
        <el-icon><Goods /></el-icon>
        <div class="empty-state-text">暂无商品</div>
        <el-button type="primary" @click="showCreateDialog = true">
          创建第一个商品
        </el-button>
      </div>

      <div v-else class="product-list">
        <div 
          v-for="product in products"
          :key="product.id"
          class="product-card card"
        >
          <div class="card-body">
            <div class="product-header">
              <div class="product-info">
                <div class="product-name">{{ product.name }}</div>
                <div class="product-category">{{ product.category || '未分类' }}</div>
              </div>
              <div class="product-actions">
                <el-dropdown @command="(command) => handleAction(command, product)">
                  <el-button type="text" :icon="MoreFilled" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">编辑</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
            
            <div class="product-details">
              <div class="detail-row">
                <span class="label">单价:</span>
                <span class="value price">¥{{ product.price }}</span>
              </div>
              <div class="detail-row">
                <span class="label">单位:</span>
                <span class="value">{{ product.unit }}</span>
              </div>
              <div v-if="product.description" class="detail-row">
                <span class="label">描述:</span>
                <span class="value description">{{ product.description }}</span>
              </div>
            </div>

            <div class="product-stats">
              <div class="stat-item">
                <span class="stat-label">库存</span>
                <span class="stat-value">{{ product.stock || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">已售</span>
                <span class="stat-value">{{ product.sold_count || 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建商品对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建商品"
      width="90%"
      :before-close="handleCloseCreate"
    >
      <ProductCreateForm 
        @success="handleCreateSuccess"
        @cancel="showCreateDialog = false"
      />
    </el-dialog>

    <!-- 编辑商品对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑商品"
      width="90%"
      :before-close="handleCloseEdit"
    >
      <ProductEditForm 
        v-if="editingProduct"
        :product="editingProduct"
        @success="handleEditSuccess"
        @cancel="showEditDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, 
  Plus, 
  Goods, 
  Loading,
  MoreFilled
} from '@element-plus/icons-vue'
import { productAPI } from '@/utils/api'
import ProductCreateForm from '@/components/ProductCreateForm.vue'
import ProductEditForm from '@/components/ProductEditForm.vue'

const loading = ref(false)
const products = ref([])
const searchQuery = ref('')
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const editingProduct = ref(null)

const loadProducts = async () => {
  loading.value = true
  try {
    const params = {}
    if (searchQuery.value.trim()) {
      params.search = searchQuery.value.trim()
    }

    const result = await productAPI.getProducts(params)
    if (result.success) {
      products.value = result.data || []
    } else {
      ElMessage.error(result.error || '加载商品失败')
    }
  } catch (error) {
    ElMessage.error('加载商品失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  loadProducts()
}

const handleAction = async (command, product) => {
  if (command === 'edit') {
    editingProduct.value = product
    showEditDialog.value = true
  } else if (command === 'delete') {
    try {
      await ElMessageBox.confirm(
        `确定要删除商品"${product.name}"吗？此操作不可恢复。`,
        '删除商品',
        {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const result = await productAPI.deleteProduct(product.id)
      if (result.success) {
        ElMessage.success('商品删除成功')
        loadProducts()
      } else {
        ElMessage.error(result.error || '删除商品失败')
      }
    } catch {
      // 用户取消
    }
  }
}

const handleCreateSuccess = () => {
  showCreateDialog.value = false
  loadProducts()
  ElMessage.success('商品创建成功')
}

const handleEditSuccess = () => {
  showEditDialog.value = false
  editingProduct.value = null
  loadProducts()
  ElMessage.success('商品更新成功')
}

const handleCloseCreate = (done) => {
  done()
}

const handleCloseEdit = (done) => {
  editingProduct.value = null
  done()
}

onMounted(() => {
  loadProducts()
})
</script>

<style scoped>
.search-section {
  background: var(--bg-color);
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.action-section {
  margin-bottom: 16px;
}

.product-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.product-card {
  transition: transform 0.2s ease;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.product-category {
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--bg-page);
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
}

.product-actions {
  margin-left: 12px;
}

.product-details {
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.label {
  color: var(--text-secondary);
  font-weight: 500;
}

.value {
  color: var(--text-primary);
  font-weight: 500;
  text-align: right;
  flex: 1;
  margin-left: 12px;
}

.price {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 16px;
}

.description {
  font-size: 13px;
  line-height: 1.4;
  max-width: 200px;
  word-break: break-word;
}

.product-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 12px;
  border-top: 1px solid var(--border-color);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}
</style>
