<template>
  <div class="app-layout">
    <!-- 简洁的顶部导航栏 -->
    <div class="top-header">
      <div class="header-content">
        <div class="app-title">
          <h3>小商店</h3>
        </div>
        <el-dropdown @command="handleCommand" trigger="click">
          <el-button type="text" class="user-btn">
            <el-avatar :size="24" icon="UserFilled" />
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="logout">
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <DemoModeAlert />
      <router-view />
    </div>

    <!-- 底部导航栏 -->
    <div class="bottom-nav">
      <div
        v-for="item in navItems"
        :key="item.path"
        @click="navigateTo(item.path)"
        class="nav-item"
        :class="{ active: $route.path === item.path }"
      >
        <el-icon :size="18">
          <component :is="item.icon" />
        </el-icon>
        <span class="nav-label">{{ item.title }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Odometer, Goods, UserFilled, Document, Calendar, Timer
} from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import DemoModeAlert from './DemoModeAlert.vue'

const router = useRouter()
const authStore = useAuthStore()

const isAdmin = computed(() => authStore.isAdmin)

// 底部导航项目
const navItems = computed(() => {
  const items = [
    { path: '/', title: '首页', icon: 'Odometer' },
    { path: '/orders', title: '订单', icon: 'Document' },
    { path: '/calendar', title: '日历', icon: 'Calendar' },
    { path: '/products', title: '商品', icon: 'Goods' }
  ]

  if (isAdmin.value) {
    items.push({ path: '/customers', title: '客户', icon: 'UserFilled' })
  }

  return items
})

const navigateTo = (path) => {
  router.push(path)
}

const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能待实现')
      break
    case 'changePassword':
      ElMessage.info('修改密码功能待实现')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const result = await authStore.signOut()
        if (result.success) {
          router.push('/login')
        }
      } catch {
        // 用户取消退出
      }
      break
  }
}

// 初始化认证状态
onMounted(async () => {
  if (!authStore.initialized) {
    await authStore.initialize()
  }
})
</script>

<style scoped>
/* 简洁的移动端布局 */
.app-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏 */
.top-header {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  max-width: 100%;
}

.app-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.user-btn {
  padding: 4px;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 60px; /* 为底部导航留空间 */
  background-color: #f5f5f5;
}

/* 底部导航栏 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: white;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 50px;
  color: #666;
}

.nav-item.active {
  color: #409eff;
}

.nav-label {
  font-size: 10px;
  font-weight: 500;
  text-align: center;
  line-height: 1;
}

/* 安全区域适配（iPhone X等） */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .bottom-nav {
    padding-bottom: env(safe-area-inset-bottom);
    height: calc(60px + env(safe-area-inset-bottom));
  }

  .main-content {
    padding-bottom: calc(70px + env(safe-area-inset-bottom));
  }
}

/* 大屏幕适配 */
@media (min-width: 768px) {
  .app-layout {
    max-width: 768px;
    margin: 0 auto;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
}
</style>
