<template>
  <div class="customer-detail">
    <!-- 基本信息 -->
    <div class="detail-section">
      <div class="section-title">基本信息</div>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">姓名:</span>
          <span class="value">{{ customer.name }}</span>
        </div>
        <div class="info-item">
          <span class="label">电话:</span>
          <span class="value">
            <a :href="`tel:${customer.contact}`" class="phone-link">
              {{ customer.contact }}
            </a>
          </span>
        </div>
        <div class="info-item">
          <span class="label">注册时间:</span>
          <span class="value">{{ formatDate(customer.created_at) }}</span>
        </div>
        <div class="info-item">
          <span class="label">最近订单:</span>
          <span class="value">{{ customer.last_order_date ? formatDate(customer.last_order_date) : '无' }}</span>
        </div>
      </div>
    </div>

    <!-- 地址信息 -->
    <div v-if="customer.addresses && customer.addresses.length > 0" class="detail-section">
      <div class="section-title">收货地址</div>
      <div class="address-list">
        <div 
          v-for="(address, index) in customer.addresses"
          :key="index"
          class="address-card"
        >
          <div class="address-header">
            <span class="address-label">{{ address.label }}</span>
            <el-tag v-if="address.is_default" size="small" type="primary">默认</el-tag>
          </div>
          <div class="address-content">{{ address.address }}</div>
          <div v-if="address.contact" class="address-contact">
            联系人: {{ address.contact }} {{ address.phone }}
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="detail-section">
      <div class="section-title">消费统计</div>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-value">{{ customer.order_count || 0 }}</div>
          <div class="stat-label">订单总数</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">¥{{ customer.total_amount || '0.00' }}</div>
          <div class="stat-label">消费总额</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">¥{{ calculateAvgAmount() }}</div>
          <div class="stat-label">平均订单</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ getCustomerLevel() }}</div>
          <div class="stat-label">客户等级</div>
        </div>
      </div>
    </div>

    <!-- 最近订单 -->
    <div class="detail-section">
      <div class="section-title">
        <span>最近订单</span>
        <el-button 
          type="text" 
          size="small"
          @click="viewAllOrders"
        >
          查看全部
        </el-button>
      </div>
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span style="margin-left: 8px;">加载中...</span>
      </div>
      <div v-else-if="recentOrders.length === 0" class="empty-state">
        <el-icon><Document /></el-icon>
        <span>暂无订单</span>
      </div>
      <div v-else class="order-list">
        <div 
          v-for="order in recentOrders"
          :key="order.id"
          class="order-item"
          @click="viewOrder(order.id)"
        >
          <div class="order-info">
            <div class="order-date">{{ formatDate(order.delivery_date) }}</div>
            <div class="order-amount">¥{{ order.total_amount }}</div>
          </div>
          <el-tag 
            :type="getStatusType(order.status)"
            size="small"
          >
            {{ getStatusText(order.status) }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 备注信息 -->
    <div v-if="customer.notes" class="detail-section">
      <div class="section-title">备注信息</div>
      <div class="notes-content">{{ customer.notes }}</div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-section">
      <el-button 
        type="primary"
        @click="createOrder"
        style="width: 100%; margin-bottom: 12px;"
      >
        为该客户创建订单
      </el-button>
      <div class="button-group">
        <el-button 
          @click="editCustomer"
          style="flex: 1;"
        >
          编辑客户
        </el-button>
        <el-button 
          @click="$emit('close')"
          style="flex: 1;"
        >
          关闭
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Document, 
  Loading 
} from '@element-plus/icons-vue'
import { orderAPI } from '@/utils/api'

const props = defineProps({
  customer: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close'])

const router = useRouter()
const loading = ref(false)
const recentOrders = ref([])

const formatDate = (dateString) => {
  if (!dateString) return '无'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const calculateAvgAmount = () => {
  const total = parseFloat(props.customer.total_amount) || 0
  const count = props.customer.order_count || 0
  if (count === 0) return '0.00'
  return (total / count).toFixed(2)
}

const getCustomerLevel = () => {
  const total = parseFloat(props.customer.total_amount) || 0
  if (total >= 10000) return 'VIP'
  if (total >= 5000) return '金牌'
  if (total >= 1000) return '银牌'
  return '普通'
}

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'production': 'primary',
    'delivery': 'info',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

const loadRecentOrders = async () => {
  loading.value = true
  try {
    const result = await orderAPI.getOrders({ 
      customer_id: props.customer.id,
      limit: 5,
      sort: 'created_at',
      order: 'desc'
    })
    
    if (result.success) {
      recentOrders.value = result.data || []
    }
  } catch (error) {
    ElMessage.error('加载订单失败')
  } finally {
    loading.value = false
  }
}

const viewAllOrders = () => {
  router.push(`/orders?customer_id=${props.customer.id}`)
  emit('close')
}

const viewOrder = (orderId) => {
  router.push(`/orders/${orderId}`)
  emit('close')
}

const createOrder = () => {
  router.push('/orders')
  emit('close')
}

const editCustomer = () => {
  // 这里可以触发编辑事件或跳转到编辑页面
  ElMessage.info('编辑功能开发中')
}

onMounted(() => {
  loadRecentOrders()
})
</script>

<style scoped>
.customer-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.detail-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.label {
  color: var(--text-secondary);
  font-weight: 500;
}

.value {
  color: var(--text-primary);
  font-weight: 500;
}

.phone-link {
  color: var(--primary-color);
  text-decoration: none;
}

.address-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.address-card {
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-page);
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.address-label {
  font-weight: 600;
  color: var(--text-primary);
}

.address-content {
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: 4px;
}

.address-contact {
  font-size: 12px;
  color: var(--text-secondary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-card {
  text-align: center;
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-page);
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.order-item:hover {
  border-color: var(--primary-color);
  background: rgba(64, 158, 255, 0.05);
}

.order-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.order-date {
  font-size: 14px;
  color: var(--text-primary);
}

.order-amount {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
}

.notes-content {
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.5;
  white-space: pre-wrap;
}

.action-section {
  margin-top: 24px;
}

.button-group {
  display: flex;
  gap: 12px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--text-secondary);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--text-secondary);
}

.empty-state .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}
</style>
