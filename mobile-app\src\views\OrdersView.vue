<template>
  <div class="page">
    <!-- 搜索和筛选 -->
    <div class="search-section">
      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索客户姓名或电话"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="filter-bar">
        <el-select
          v-model="statusFilter"
          placeholder="订单状态"
          clearable
          @change="handleFilter"
          style="width: 120px"
        >
          <el-option label="待处理" value="pending" />
          <el-option label="生产中" value="production" />
          <el-option label="配送中" value="delivery" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
        <el-select
          v-model="paymentFilter"
          placeholder="付款状态"
          clearable
          @change="handleFilter"
          style="width: 120px"
        >
          <el-option label="已付款" value="paid" />
          <el-option label="未付款" value="unpaid" />
        </el-select>
        <el-date-picker
          v-model="dateFilter"
          type="date"
          placeholder="选择日期"
          clearable
          @change="handleFilter"
          style="width: 140px"
        />
      </div>
    </div>

    <div class="page-content">
      <!-- 新建订单按钮 -->
      <div class="action-section">
        <el-button 
          type="primary" 
          @click="showCreateDialog = true"
          style="width: 100%"
        >
          <el-icon><Plus /></el-icon>
          新建订单
        </el-button>
      </div>

      <!-- 订单列表 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span style="margin-left: 8px;">加载中...</span>
      </div>
      
      <div v-else-if="orders.length === 0" class="empty-state">
        <el-icon><Document /></el-icon>
        <div class="empty-state-text">暂无订单</div>
        <el-button type="primary" @click="showCreateDialog = true">
          创建第一个订单
        </el-button>
      </div>

      <div v-else class="order-list">
        <div 
          v-for="order in orders"
          :key="order.id"
          class="order-card card touch-feedback"
          @click="viewOrderDetail(order.id)"
        >
          <div class="card-body">
            <div class="order-header">
              <div class="order-info">
                <div class="customer-name">{{ order.customer_name }}</div>
                <div class="order-meta">
                  订单号: {{ order.id }}
                </div>
              </div>
              <div class="order-tags">
                <el-tag
                  :type="getStatusType(order.status)"
                  size="small"
                >
                  {{ getStatusText(order.status) }}
                </el-tag>
                <el-tag
                  :type="getPaymentType(order.payment_status)"
                  size="small"
                  style="margin-left: 4px;"
                >
                  {{ getPaymentText(order.payment_status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="order-details">
              <div class="detail-row">
                <span class="label">交付日期:</span>
                <span class="value">{{ formatDate(order.delivery_date) }}</span>
              </div>
              <div class="detail-row">
                <span class="label">联系电话:</span>
                <span class="value">{{ order.customer_phone }}</span>
              </div>
              <div class="detail-row">
                <span class="label">订单金额:</span>
                <span class="value amount">¥{{ order.total_amount }}</span>
              </div>
            </div>

            <div v-if="order.items && order.items.length > 0" class="order-items">
              <div class="items-summary">
                <span class="items-count">{{ order.items.length }} 种商品</span>
                <span class="items-preview">
                  {{ order.items.slice(0, 2).map(item => item.product_name).join(', ') }}
                  <span v-if="order.items.length > 2">等</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore && !loading" class="load-more">
        <el-button @click="loadMore" :loading="loadingMore">
          加载更多
        </el-button>
      </div>
    </div>

    <!-- 创建订单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建订单"
      width="90%"
      :before-close="handleCloseCreate"
    >
      <OrderCreateForm 
        @success="handleCreateSuccess"
        @cancel="showCreateDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Plus, 
  Document, 
  Loading 
} from '@element-plus/icons-vue'
import { orderAPI } from '@/utils/api'
import OrderCreateForm from '@/components/OrderCreateForm.vue'

const router = useRouter()
const loading = ref(false)
const loadingMore = ref(false)
const orders = ref([])
const searchQuery = ref('')
const statusFilter = ref('')
const paymentFilter = ref('')
const dateFilter = ref('')
const showCreateDialog = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'production': 'primary',
    'delivery': 'info',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

const getPaymentType = (paymentStatus) => {
  const paymentMap = {
    'paid': 'success',
    'unpaid': 'warning',
    'partial': 'info'
  }
  return paymentMap[paymentStatus] || 'info'
}

const getPaymentText = (paymentStatus) => {
  const paymentMap = {
    'paid': '已付款',
    'unpaid': '未付款',
    'partial': '部分付款'
  }
  return paymentMap[paymentStatus] || '未知'
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const buildQueryParams = () => {
  const params = {
    page: currentPage.value,
    limit: pageSize.value,
    sort: 'created_at',
    order: 'desc'
  }

  if (searchQuery.value.trim()) {
    params.search = searchQuery.value.trim()
  }

  if (statusFilter.value) {
    params.status = statusFilter.value
  }

  if (paymentFilter.value) {
    params.payment_status = paymentFilter.value
  }

  if (dateFilter.value) {
    params.delivery_date = dateFilter.value
  }

  return params
}

const loadOrders = async (append = false) => {
  if (!append) {
    loading.value = true
    currentPage.value = 1
  } else {
    loadingMore.value = true
  }

  try {
    const params = buildQueryParams()
    const result = await orderAPI.getOrders(params)

    if (result.success) {
      const newOrders = result.data || []
      
      if (append) {
        orders.value = [...orders.value, ...newOrders]
      } else {
        orders.value = newOrders
      }

      hasMore.value = newOrders.length === pageSize.value
    } else {
      ElMessage.error(result.error || '加载订单失败')
    }
  } catch (error) {
    ElMessage.error('加载订单失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

const handleSearch = () => {
  loadOrders()
}

const handleFilter = () => {
  loadOrders()
}

const loadMore = () => {
  currentPage.value++
  loadOrders(true)
}

const viewOrderDetail = (orderId) => {
  router.push(`/orders/${orderId}`)
}

const handleCreateSuccess = () => {
  showCreateDialog.value = false
  loadOrders()
  ElMessage.success('订单创建成功')
}

const handleCloseCreate = (done) => {
  done()
}

onMounted(() => {
  loadOrders()
})
</script>

<style scoped>
.search-section {
  background: var(--bg-color);
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.search-bar {
  margin-bottom: 12px;
}

.filter-bar {
  display: flex;
  gap: 12px;
}

.action-section {
  margin-bottom: 16px;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-card {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.order-card:active {
  transform: scale(0.98);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.order-tags {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
}

.customer-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.order-meta {
  font-size: 12px;
  color: var(--text-secondary);
}

.order-details {
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 14px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.label {
  color: var(--text-secondary);
}

.value {
  color: var(--text-primary);
  font-weight: 500;
}

.amount {
  color: var(--primary-color);
  font-weight: 600;
}

.order-items {
  padding-top: 12px;
  border-top: 1px solid var(--border-color);
}

.items-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.items-count {
  color: var(--text-secondary);
}

.items-preview {
  color: var(--text-primary);
  text-align: right;
  flex: 1;
  margin-left: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.load-more {
  text-align: center;
  padding: 20px 0;
}
</style>
