<template>
  <div class="page">
    <!-- 搜索和筛选 -->
    <div class="search-section">
      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索客户姓名或电话"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="filter-bar">
        <el-select
          v-model="statusFilter"
          placeholder="订单状态"
          clearable
          @change="handleFilter"
          style="width: 120px"
        >
          <el-option label="待处理" value="pending" />
          <el-option label="生产中" value="production" />
          <el-option label="配送中" value="delivery" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
        <el-select
          v-model="paymentFilter"
          placeholder="付款状态"
          clearable
          @change="handleFilter"
          style="width: 120px"
        >
          <el-option label="已付款" value="paid" />
          <el-option label="未付款" value="unpaid" />
        </el-select>
        <el-date-picker
          v-model="dateFilter"
          type="date"
          placeholder="选择日期"
          clearable
          @change="handleFilter"
          style="width: 140px"
        />
      </div>
    </div>

    <div class="page-content">
      <!-- 新建订单按钮 -->
      <div class="action-section">
        <el-button
          type="primary"
          @click="createOrder"
          style="width: 100%"
        >
          <el-icon><Plus /></el-icon>
          新建订单
        </el-button>
      </div>

      <!-- 订单列表 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span style="margin-left: 8px;">加载中...</span>
      </div>
      
      <div v-else-if="orders.length === 0" class="empty-state">
        <el-icon><Document /></el-icon>
        <div class="empty-state-text">暂无订单</div>
        <el-button type="primary" @click="createOrder">
          创建第一个订单
        </el-button>
      </div>

      <div v-else class="order-list">
        <div 
          v-for="order in orders"
          :key="order.id"
          class="order-card card touch-feedback"
          @click="viewOrderDetail(order.id)"
        >
          <div class="card-body">
            <div class="order-header">
              <div class="order-info">
                <div class="customer-name">{{ order.customer_name }}</div>
                <div class="order-meta">
                  订单号: {{ order.id }}
                </div>
              </div>
              <div class="order-tags">
                <el-tag
                  :type="getStatusType(order.order_status)"
                  size="small"
                >
                  {{ getStatusText(order.order_status) }}
                </el-tag>
                <el-tag
                  :type="getPaymentType(order.payment_status)"
                  size="small"
                  style="margin-left: 4px;"
                >
                  {{ getPaymentText(order.payment_status) }}
                </el-tag>
              </div>
            </div>
            
            <div class="order-details">
              <div class="detail-row">
                <span class="label">交付日期:</span>
                <span class="value">{{ formatDate(order.delivery_datetime) }}</span>
              </div>
              <div class="detail-row">
                <span class="label">客户信息:</span>
                <span class="value">{{ order.customer?.name || '未知客户' }}</span>
              </div>
              <div class="detail-row">
                <span class="label">订单金额:</span>
                <span class="value amount">¥{{ order.total_price }}</span>
              </div>
            </div>

            <div v-if="order.order_items && order.order_items.length > 0" class="order-items">
              <div class="items-summary">
                <span class="items-count">{{ order.order_items.length }} 种商品</span>
                <span class="items-preview">
                  {{ order.order_items.slice(0, 2).map(item => item.product?.name || '商品').join(', ') }}
                  <span v-if="order.order_items.length > 2">等</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore && !loading" class="load-more">
        <el-button @click="loadMore" :loading="loadingMore">
          加载更多
        </el-button>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Plus, 
  Document, 
  Loading 
} from '@element-plus/icons-vue'
import { orderAPI } from '@/utils/api'

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const loadingMore = ref(false)
const orders = ref([])
const searchQuery = ref('')
const statusFilter = ref('')
const paymentFilter = ref('')
const dateFilter = ref('')

const currentPage = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'production': 'primary',
    'delivery': 'info',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

const getPaymentType = (paymentStatus) => {
  const paymentMap = {
    'paid': 'success',
    'unpaid': 'warning',
    'partial': 'info'
  }
  return paymentMap[paymentStatus] || 'info'
}

const getPaymentText = (paymentStatus) => {
  const paymentMap = {
    'paid': '已付款',
    'unpaid': '未付款',
    'partial': '部分付款'
  }
  return paymentMap[paymentStatus] || '未知'
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const buildQueryParams = () => {
  const params = {
    page: currentPage.value,
    limit: pageSize.value,
    sort: 'created_at',
    order: 'desc'
  }

  if (searchQuery.value.trim()) {
    params.search = searchQuery.value.trim()
  }

  if (statusFilter.value) {
    params.order_status = statusFilter.value
  }

  if (paymentFilter.value) {
    params.payment_status = paymentFilter.value
  }

  if (dateFilter.value) {
    params.delivery_datetime = dateFilter.value
  }

  return params
}

const loadOrders = async (append = false) => {
  if (!append) {
    loading.value = true
    currentPage.value = 1
  } else {
    loadingMore.value = true
  }

  try {
    const params = buildQueryParams()
    const result = await orderAPI.getOrders(params)

    if (result.success) {
      const newOrders = result.data || []
      
      if (append) {
        orders.value = [...orders.value, ...newOrders]
      } else {
        orders.value = newOrders
      }

      hasMore.value = newOrders.length === pageSize.value
    } else {
      ElMessage.error(result.error || '加载订单失败')
    }
  } catch (error) {
    ElMessage.error('加载订单失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

const handleSearch = () => {
  loadOrders()
}

const handleFilter = () => {
  loadOrders()
}

const loadMore = () => {
  currentPage.value++
  loadOrders(true)
}

const viewOrderDetail = (orderId) => {
  router.push(`/orders/${orderId}`)
}

const createOrder = () => {
  router.push('/orders/create')
}

// 初始化筛选参数
const initFiltersFromRoute = () => {
  const query = route.query

  // 处理订单状态筛选
  if (query.status) {
    statusFilter.value = query.status
  }

  // 处理付款状态筛选
  if (query.payment_status) {
    paymentFilter.value = query.payment_status
  }

  // 处理日期筛选
  if (query.date) {
    dateFilter.value = query.date
  }

  console.log('Route query:', query)
  console.log('Applied filters:', {
    status: statusFilter.value,
    payment: paymentFilter.value,
    date: dateFilter.value
  })
}

// 监听路由变化
watch(() => route.query, () => {
  initFiltersFromRoute()
  loadOrders()
}, { immediate: false })

onMounted(() => {
  initFiltersFromRoute()
  loadOrders()
})
</script>

<style scoped>
.search-section {
  background: var(--bg-color);
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.search-bar {
  margin-bottom: 12px;
}

.filter-bar {
  display: flex;
  gap: 12px;
}

.action-section {
  margin-bottom: 16px;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-card {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.order-card:active {
  transform: scale(0.98);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.order-tags {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
}

.customer-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.order-meta {
  font-size: 12px;
  color: var(--text-secondary);
}

.order-details {
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 14px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.label {
  color: var(--text-secondary);
}

.value {
  color: var(--text-primary);
  font-weight: 500;
}

.amount {
  color: var(--primary-color);
  font-weight: 600;
}

.order-items {
  padding-top: 12px;
  border-top: 1px solid var(--border-color);
}

.items-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.items-count {
  color: var(--text-secondary);
}

.items-preview {
  color: var(--text-primary);
  text-align: right;
  flex: 1;
  margin-left: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.load-more {
  text-align: center;
  padding: 20px 0;
}
</style>
