import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { authAPI, getAuthToken, setAuthToken } from '../lib/api'
import { demoUsers } from '../lib/demo-data'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const userProfile = ref(null)
  const loading = ref(false)
  const initialized = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!user.value)
  const isAdmin = computed(() => userProfile.value?.role === 'super_admin')
  const isStaff = computed(() => userProfile.value?.role === 'staff')
  const userRole = computed(() => userProfile.value?.role || null)

  // 初始化认证状态
  const initialize = async () => {
    try {
      loading.value = true

      // 检查是否有保存的token
      const token = getAuthToken()
      if (token) {
        // 尝试获取用户信息
        const result = await authAPI.getProfile()
        if (result.success) {
          user.value = {
            id: result.data.id,
            email: result.data.email
          }
          userProfile.value = result.data
        } else {
          // token无效，清除并尝试自动登录
          setAuthToken(null)
          await autoLogin()
        }
      } else {
        // 没有token，尝试自动登录
        await autoLogin()
      }

    } catch (error) {
      console.error('初始化认证失败:', error)
      // 清除无效token
      setAuthToken(null)
    } finally {
      loading.value = false
      initialized.value = true
    }
  }

  // 自动登录（使用默认管理员账户）
  const autoLogin = async () => {
    try {
      const result = await authAPI.login('<EMAIL>', 'demo123')
      if (result.success) {
        setAuthToken(result.data.token)
        user.value = {
          id: result.data.user.id,
          email: result.data.user.email
        }
        userProfile.value = result.data.user
        console.log('自动登录成功')
      }
    } catch (error) {
      console.warn('自动登录失败:', error)
    }
  }

  // 获取用户档案
  const fetchUserProfile = async () => {
    if (!user.value) return

    // 如果Supabase未配置，从演示数据中查找
    if (!isSupabaseConfigured) {
      const demoUser = demoUsers.find(u => u.id === user.value.id)
      if (demoUser) {
        userProfile.value = demoUser
      }
      return
    }

    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.value.id)
        .single()

      if (error) {
        console.error('获取用户档案失败:', error)
        return
      }

      userProfile.value = data
    } catch (error) {
      console.error('获取用户档案异常:', error)
    }
  }

  // 登录
  const signIn = async (email, password) => {
    try {
      loading.value = true

      const result = await authAPI.login(email, password)

      if (result.success) {
        // 设置token
        setAuthToken(result.data.token)
        user.value = {
          id: result.data.user.id,
          email: result.data.user.email
        }
        userProfile.value = result.data.user

        ElMessage.success('登录成功')
        return { success: true, data: { user: result.data.user } }
      } else {
        throw new Error(result.error)
      }

    } catch (error) {
      console.error('登录失败:', error)
      ElMessage.error(error.message || '登录失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 注册（仅超级管理员可用）
  const signUp = async (email, password, role = 'staff') => {
    try {
      loading.value = true

      // 如果Supabase未配置，显示提示
      if (!isSupabaseConfigured) {
        ElMessage.warning('演示模式下无法创建真实用户，请配置Supabase')
        return { success: false, error: new Error('演示模式限制') }
      }

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            role: role
          }
        }
      })

      if (error) {
        throw error
      }

      ElMessage.success('用户创建成功')
      return { success: true, data }

    } catch (error) {
      console.error('注册失败:', error)
      ElMessage.error(error.message || '注册失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 登出
  const signOut = async () => {
    try {
      loading.value = true

      // 清除token和用户状态
      setAuthToken(null)
      user.value = null
      userProfile.value = null

      ElMessage.success('已退出登录')
      return { success: true }

    } catch (error) {
      console.error('登出失败:', error)
      ElMessage.error(error.message || '登出失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const updatePassword = async (newPassword) => {
    try {
      loading.value = true

      // 如果Supabase未配置，显示提示
      if (!isSupabaseConfigured) {
        ElMessage.warning('演示模式下无法修改密码，请配置Supabase')
        return { success: false, error: new Error('演示模式限制') }
      }

      const { error } = await supabase.auth.updateUser({
        password: newPassword
      })

      if (error) {
        throw error
      }

      ElMessage.success('密码修改成功')
      return { success: true }

    } catch (error) {
      console.error('修改密码失败:', error)
      ElMessage.error(error.message || '修改密码失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 重置密码
  const resetPassword = async (email) => {
    try {
      loading.value = true

      // 如果Supabase未配置，显示提示
      if (!isSupabaseConfigured) {
        ElMessage.warning('演示模式下无法重置密码，请配置Supabase')
        return { success: false, error: new Error('演示模式限制') }
      }

      const { error } = await supabase.auth.resetPasswordForEmail(email)

      if (error) {
        throw error
      }

      ElMessage.success('密码重置邮件已发送')
      return { success: true }

    } catch (error) {
      console.error('重置密码失败:', error)
      ElMessage.error(error.message || '重置密码失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  // 更新用户档案
  const updateProfile = async (updates) => {
    try {
      loading.value = true

      // 如果Supabase未配置，显示提示
      if (!isSupabaseConfigured) {
        ElMessage.warning('演示模式下无法更新档案，请配置Supabase')
        return { success: false, error: new Error('演示模式限制') }
      }

      const { data, error } = await supabase
        .from('user_profiles')
        .update(updates)
        .eq('id', user.value.id)
        .select()
        .single()

      if (error) {
        throw error
      }

      userProfile.value = data
      ElMessage.success('档案更新成功')
      return { success: true, data }

    } catch (error) {
      console.error('更新档案失败:', error)
      ElMessage.error(error.message || '更新档案失败')
      return { success: false, error }
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    user,
    userProfile,
    loading,
    initialized,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    isStaff,
    userRole,
    
    // 方法
    initialize,
    fetchUserProfile,
    signIn,
    signUp,
    signOut,
    updatePassword,
    resetPassword,
    updateProfile
  }
})
