package services

import (
	"fmt"
	"log"
	"time"

	"shop-order-backend/internal/models"

	"github.com/robfig/cron/v3"
	"xorm.io/xorm"
)

type SchedulerService struct {
	db             *xorm.Engine
	cron           *cron.Cron
	holidayService *HolidayService
	startTime      time.Time
}

func NewSchedulerService(db *xorm.Engine) *SchedulerService {
	// 创建带有秒级精度的cron实例
	c := cron.New(cron.WithSeconds())

	return &SchedulerService{
		db:             db,
		cron:           c,
		holidayService: NewHolidayService(db),
	}
}

// Start 启动定时任务
func (s *SchedulerService) Start() error {
	s.startTime = time.Now()
	log.Println("Starting scheduler service...")

	// 每天凌晨2点同步当年节假日数据
	_, err := s.cron.AddFunc("0 0 2 * * *", s.syncCurrentYearHolidays)
	if err != nil {
		return err
	}

	// 每年1月1日凌晨3点同步下一年节假日数据
	_, err = s.cron.AddFunc("0 0 3 1 1 *", s.syncNextYearHolidays)
	if err != nil {
		return err
	}

	// 每小时清理过期的临时数据（如果有的话）
	_, err = s.cron.AddFunc("0 0 * * * *", s.cleanupExpiredData)
	if err != nil {
		return err
	}

	// 每天凌晨1点备份重要数据统计
	_, err = s.cron.AddFunc("0 0 1 * * *", s.generateDailyStats)
	if err != nil {
		return err
	}

	// 每天凌晨4点同步皇历数据（可选功能）
	_, err = s.cron.AddFunc("0 0 4 * * *", s.syncLunarCalendar)
	if err != nil {
		return err
	}

	// 启动cron
	s.cron.Start()
	log.Println("Scheduler service started successfully")

	return nil
}

// Stop 停止定时任务
func (s *SchedulerService) Stop() {
	log.Println("Stopping scheduler service...")
	s.cron.Stop()
	log.Println("Scheduler service stopped")
}

// syncCurrentYearHolidays 同步当年节假日数据
func (s *SchedulerService) syncCurrentYearHolidays() {
	currentYear := time.Now().Year()
	log.Printf("Syncing holidays for year %d...", currentYear)

	err := s.holidayService.SyncHolidays(currentYear)
	if err != nil {
		log.Printf("Failed to sync holidays for year %d: %v", currentYear, err)
	} else {
		log.Printf("Successfully synced holidays for year %d", currentYear)
	}
}

// syncNextYearHolidays 同步下一年节假日数据
func (s *SchedulerService) syncNextYearHolidays() {
	nextYear := time.Now().Year() + 1
	log.Printf("Syncing holidays for next year %d...", nextYear)

	err := s.holidayService.SyncHolidays(nextYear)
	if err != nil {
		log.Printf("Failed to sync holidays for next year %d: %v", nextYear, err)
	} else {
		log.Printf("Successfully synced holidays for next year %d", nextYear)
	}
}

// cleanupExpiredData 清理过期数据
func (s *SchedulerService) cleanupExpiredData() {
	log.Println("Cleaning up expired data...")

	// 清理超过2年的节假日数据
	cutoffDate := time.Now().AddDate(-2, 0, 0)
	_, err := s.db.Where("created_at < ?", cutoffDate).Delete(&models.Holiday{})
	if err != nil {
		log.Printf("Failed to cleanup old holidays: %v", err)
	}

	// 清理超过1年的订单状态历史
	_, err = s.db.Exec("DELETE FROM order_status_history WHERE created_at < ?", cutoffDate)
	if err != nil {
		log.Printf("Failed to cleanup old order status history: %v", err)
	}

	log.Println("Expired data cleanup completed")
}

// generateDailyStats 生成每日统计数据
func (s *SchedulerService) generateDailyStats() {
	yesterday := time.Now().AddDate(0, 0, -1)
	log.Printf("Generating daily stats for %s...", yesterday.Format("2006-01-02"))

	// 统计昨日订单数据
	var orderCount int64
	var totalRevenue float64

	// 获取昨日订单数量
	orderCount, err := s.db.Where("DATE(created_at) = ?", yesterday.Format("2006-01-02")).Count(&models.Order{})
	if err != nil {
		log.Printf("Failed to count daily orders: %v", err)
		return
	}

	// 获取昨日总收入
	_, err = s.db.Table("orders").
		Where("DATE(created_at) = ? AND payment_status = 'paid'", yesterday.Format("2006-01-02")).
		Sum(&models.Order{}, "total_price")
	if err != nil {
		log.Printf("Failed to calculate daily revenue: %v", err)
		return
	}

	// 获取昨日新客户数量
	newCustomerCount, err := s.db.Where("DATE(created_at) = ?", yesterday.Format("2006-01-02")).Count(&models.Customer{})
	if err != nil {
		log.Printf("Failed to count new customers: %v", err)
		return
	}

	// 获取低库存商品数量
	lowStockCount, err := s.db.Where("stock_quantity <= min_stock_level").Count(&models.Product{})
	if err != nil {
		log.Printf("Failed to count low stock products: %v", err)
		return
	}

	log.Printf("Daily stats for %s: Orders=%d, Revenue=%.2f, New Customers=%d, Low Stock Products=%d",
		yesterday.Format("2006-01-02"), orderCount, totalRevenue, newCustomerCount, lowStockCount)

	// 这里可以将统计数据保存到数据库或发送通知
	// 暂时只记录日志
}

// AddCustomJob 添加自定义定时任务
func (s *SchedulerService) AddCustomJob(spec string, cmd func()) (cron.EntryID, error) {
	return s.cron.AddFunc(spec, cmd)
}

// RemoveJob 移除定时任务
func (s *SchedulerService) RemoveJob(id cron.EntryID) {
	s.cron.Remove(id)
}

// GetJobs 获取所有定时任务
func (s *SchedulerService) GetJobs() []cron.Entry {
	return s.cron.Entries()
}

// RunJobNow 立即执行指定任务
func (s *SchedulerService) RunJobNow(jobName string) error {
	switch jobName {
	case "sync_current_year_holidays":
		go s.syncCurrentYearHolidays()
	case "sync_next_year_holidays":
		go s.syncNextYearHolidays()
	case "cleanup_expired_data":
		go s.cleanupExpiredData()
	case "generate_daily_stats":
		go s.generateDailyStats()
	case "sync_lunar_calendar":
		go s.syncLunarCalendar()
	default:
		return fmt.Errorf("unknown job name: %s", jobName)
	}
	return nil
}

// GetJobStatus 获取定时任务状态
func (s *SchedulerService) GetJobStatus() map[string]interface{} {
	entries := s.cron.Entries()

	status := map[string]interface{}{
		"is_running": len(entries) > 0,
		"job_count":  len(entries),
		"uptime":     time.Since(s.startTime).Seconds(),
		"next_runs":  make([]map[string]interface{}, 0),
	}

	for i, entry := range entries {
		jobInfo := map[string]interface{}{
			"id":       entry.ID,
			"next_run": entry.Next,
			"prev_run": entry.Prev,
		}

		// 根据索引推断任务名称（简化版本）
		switch i {
		case 0:
			jobInfo["name"] = "sync_current_year_holidays"
		case 1:
			jobInfo["name"] = "sync_next_year_holidays"
		case 2:
			jobInfo["name"] = "cleanup_expired_data"
		case 3:
			jobInfo["name"] = "generate_daily_stats"
		case 4:
			jobInfo["name"] = "sync_lunar_calendar"
		default:
			jobInfo["name"] = "custom_job"
		}

		status["next_runs"] = append(status["next_runs"].([]map[string]interface{}), jobInfo)
	}

	return status
}

// syncLunarCalendar 同步皇历数据（可选功能）
func (s *SchedulerService) syncLunarCalendar() {
	log.Println("Syncing lunar calendar data...")

	// 这里可以添加皇历数据同步逻辑
	// 例如：获取黄道吉日、宜忌事项等
	// 目前作为占位符，可以根据需要接入第三方皇历API

	// 示例：可以接入以下类型的皇历数据
	// - 黄道吉日
	// - 宜忌事项（宜：开业、搬家、结婚等；忌：动土、出行等）
	// - 五行属性
	// - 冲煞方位

	log.Println("Lunar calendar data sync completed")
}
