package controllers

import (
	"net/http"
	"strconv"
	"time"

	"shop-order-backend/internal/models"
	"shop-order-backend/internal/services"

	"github.com/gin-gonic/gin"
	"xorm.io/xorm"
)

type OrderController struct {
	db                *xorm.Engine
	statisticsService *services.OrderStatisticsService
}

func NewOrderController(db *xorm.Engine) *OrderController {
	return &OrderController{
		db:                db,
		statisticsService: services.NewOrderStatisticsService(db),
	}
}

// GetOrders 获取订单列表
func (oc *OrderController) GetOrders(c *gin.Context) {
	var orders []models.Order

	// 查询参数
	status := c.Query("status")
	orderStatus := c.Query("order_status")
	paymentStatus := c.Query("payment_status")
	customerID := c.Query("customer_id")
	deliveryDate := c.Query("delivery_datetime")
	search := c.Query("search")

	session := oc.db.NewSession()
	defer session.Close()

	// 根据查询参数过滤
	// 支持两种状态参数格式：status 和 order_status
	if status != "" {
		session = session.Where("order_status = ?", status)
	} else if orderStatus != "" {
		session = session.Where("order_status = ?", orderStatus)
	}

	if paymentStatus != "" {
		session = session.Where("payment_status = ?", paymentStatus)
	}

	if customerID != "" {
		if id, err := strconv.ParseInt(customerID, 10, 64); err == nil {
			session = session.Where("customer_id = ?", id)
		}
	}

	// 按交付日期筛选
	if deliveryDate != "" {
		// 支持日期格式：YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss
		if len(deliveryDate) == 10 {
			// 日期格式，查询当天的订单
			// SQLite 使用 strftime 函数进行日期比较
			session = session.Where("strftime('%Y-%m-%d', delivery_datetime) = ?", deliveryDate)
		} else {
			// 完整日期时间格式
			session = session.Where("delivery_datetime = ?", deliveryDate)
		}
	}

	// 搜索功能（客户姓名或备注）
	if search != "" {
		session = session.Join("LEFT", "customer", "order.customer_id = customer.id").
			Where("customer.name LIKE ? OR order.notes LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if err := session.OrderBy("created_at DESC").Find(&orders); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch orders",
		})
		return
	}

	// 加载关联数据
	for i := range orders {
		oc.loadOrderRelations(&orders[i])
	}

	c.JSON(http.StatusOK, gin.H{
		"data": orders,
	})
}

// GetOrder 获取单个订单信息
func (oc *OrderController) GetOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid order ID",
		})
		return
	}

	var order models.Order
	has, err := oc.db.ID(id).Get(&order)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Order not found",
		})
		return
	}

	// 加载关联数据
	oc.loadOrderRelations(&order)

	c.JSON(http.StatusOK, gin.H{
		"data": order,
	})
}

// CreateOrder 创建订单
func (oc *OrderController) CreateOrder(c *gin.Context) {
	var req models.OrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 验证客户是否存在
	var customer models.Customer
	has, err := oc.db.ID(req.CustomerID).Get(&customer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}
	if !has {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Customer not found",
		})
		return
	}

	// 计算总价
	var totalPrice float64
	var orderItems []*models.OrderItem

	for _, item := range req.OrderItems {
		// 验证商品是否存在
		var product models.Product
		has, err := oc.db.ID(item.ProductID).Get(&product)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Database error",
			})
			return
		}
		if !has {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Product not found: " + strconv.FormatInt(item.ProductID, 10),
			})
			return
		}

		orderItem := &models.OrderItem{
			ProductID:          item.ProductID,
			Quantity:           item.Quantity,
			PriceAtOrder:       product.Price,
			SelectedAttributes: item.SelectedAttributes,
		}
		orderItems = append(orderItems, orderItem)
		totalPrice += product.Price * float64(item.Quantity)
	}

	// 设置默认状态
	paymentStatus := req.PaymentStatus
	if paymentStatus == "" {
		paymentStatus = "unpaid"
	}

	orderStatus := req.OrderStatus
	if orderStatus == "" {
		orderStatus = "pending"
	}

	// 创建订单
	order := &models.Order{
		CustomerID:       req.CustomerID,
		DeliveryAddress:  req.DeliveryAddress,
		DeliveryDatetime: req.DeliveryDatetime,
		TotalPrice:       totalPrice,
		PaymentStatus:    paymentStatus,
		OrderStatus:      orderStatus,
		Notes:            req.Notes,
	}

	// 开始事务
	session := oc.db.NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to start transaction",
		})
		return
	}

	// 插入订单
	if _, err := session.Insert(order); err != nil {
		session.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create order",
		})
		return
	}

	// 插入订单项
	for _, item := range orderItems {
		item.OrderID = order.ID
		if _, err := session.Insert(item); err != nil {
			session.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to create order items",
			})
			return
		}
	}

	if err := session.Commit(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to commit transaction",
		})
		return
	}

	// 加载关联数据
	oc.loadOrderRelations(order)

	c.JSON(http.StatusCreated, gin.H{
		"data": order,
	})
}

// UpdateOrder 更新订单信息
func (oc *OrderController) UpdateOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid order ID",
		})
		return
	}

	var req struct {
		DeliveryAddress  string    `json:"delivery_address"`
		DeliveryDatetime time.Time `json:"delivery_datetime"`
		PaymentStatus    string    `json:"payment_status"`
		OrderStatus      string    `json:"order_status"`
		Notes            string    `json:"notes"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 检查订单是否存在
	var order models.Order
	has, err := oc.db.ID(id).Get(&order)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Order not found",
		})
		return
	}

	// 更新订单信息
	if req.DeliveryAddress != "" {
		order.DeliveryAddress = req.DeliveryAddress
	}
	if !req.DeliveryDatetime.IsZero() {
		order.DeliveryDatetime = req.DeliveryDatetime
	}
	if req.PaymentStatus != "" {
		order.PaymentStatus = req.PaymentStatus
	}
	if req.OrderStatus != "" {
		order.OrderStatus = req.OrderStatus
	}
	order.Notes = req.Notes

	if _, err := oc.db.ID(id).Update(&order); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update order",
		})
		return
	}

	// 加载关联数据
	oc.loadOrderRelations(&order)

	c.JSON(http.StatusOK, gin.H{
		"data": order,
	})
}

// DeleteOrder 删除订单
func (oc *OrderController) DeleteOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid order ID",
		})
		return
	}

	// 检查订单是否存在
	var order models.Order
	has, err := oc.db.ID(id).Get(&order)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Order not found",
		})
		return
	}

	// 删除订单（订单项会因为外键约束自动删除）
	if _, err := oc.db.ID(id).Delete(&models.Order{}); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete order",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Order deleted successfully",
	})
}

// GetOrderStatistics 获取订单统计数据
func (oc *OrderController) GetOrderStatistics(c *gin.Context) {
	// 获取查询参数
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	groupBy := c.DefaultQuery("group_by", "category")

	// 解析日期
	var startDate, endDate time.Time
	var err error

	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid start_date format, use YYYY-MM-DD",
			})
			return
		}
	} else {
		// 默认为最近30天
		startDate = time.Now().AddDate(0, 0, -30)
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid end_date format, use YYYY-MM-DD",
			})
			return
		}
	} else {
		endDate = time.Now()
	}

	// 获取统计数据
	statistics, err := oc.statisticsService.GetOrderStatisticsByDateRange(startDate, endDate, groupBy)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get order statistics",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": statistics,
		"params": gin.H{
			"start_date": startDate.Format("2006-01-02"),
			"end_date":   endDate.Format("2006-01-02"),
			"group_by":   groupBy,
		},
	})
}

// GetDailyOrderSummary 获取指定日期的订单汇总
func (oc *OrderController) GetDailyOrderSummary(c *gin.Context) {
	dateStr := c.Param("date")
	if dateStr == "" {
		dateStr = time.Now().Format("2006-01-02")
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid date format, use YYYY-MM-DD",
		})
		return
	}

	summary, err := oc.statisticsService.GetDailyOrderSummary(date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get daily order summary",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": summary,
	})
}

// GetMonthlyOrderSummary 获取月度订单汇总
func (oc *OrderController) GetMonthlyOrderSummary(c *gin.Context) {
	yearStr := c.DefaultQuery("year", strconv.Itoa(time.Now().Year()))
	monthStr := c.DefaultQuery("month", strconv.Itoa(int(time.Now().Month())))

	year, err := strconv.Atoi(yearStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid year format",
		})
		return
	}

	month, err := strconv.Atoi(monthStr)
	if err != nil || month < 1 || month > 12 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid month format, use 1-12",
		})
		return
	}

	summaries, err := oc.statisticsService.GetMonthlyOrderSummary(year, month)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get monthly order summary",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": summaries,
		"params": gin.H{
			"year":  year,
			"month": month,
		},
	})
}

// GetCategoryTrend 获取分类趋势
func (oc *OrderController) GetCategoryTrend(c *gin.Context) {
	category := c.Query("category")
	if category == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Category parameter is required",
		})
		return
	}

	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid days parameter",
		})
		return
	}

	trend, err := oc.statisticsService.GetCategoryTrend(category, days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get category trend",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": trend,
		"params": gin.H{
			"category": category,
			"days":     days,
		},
	})
}

// loadOrderRelations 加载订单关联数据
func (oc *OrderController) loadOrderRelations(order *models.Order) {
	// 加载客户信息
	var customer models.Customer
	if has, err := oc.db.ID(order.CustomerID).Get(&customer); err == nil && has {
		order.Customer = &customer
	}

	// 加载订单项
	var orderItems []models.OrderItem
	if err := oc.db.Where("order_id = ?", order.ID).Find(&orderItems); err == nil {
		order.OrderItems = make([]*models.OrderItem, len(orderItems))
		for i := range orderItems {
			order.OrderItems[i] = &orderItems[i]

			// 加载商品信息
			var product models.Product
			if has, err := oc.db.ID(orderItems[i].ProductID).Get(&product); err == nil && has {
				order.OrderItems[i].Product = &product
			}
		}
	}
}
