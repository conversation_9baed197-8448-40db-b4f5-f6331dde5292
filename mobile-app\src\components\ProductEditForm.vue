<template>
  <div class="product-edit-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="商品名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入商品名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="商品分类">
        <el-select
          v-model="form.category"
          placeholder="请选择或输入分类"
          filterable
          allow-create
          style="width: 100%"
        >
          <el-option
            v-for="category in commonCategories"
            :key="category"
            :label="category"
            :value="category"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="商品价格" prop="price">
        <el-input-number
          v-model="form.price"
          :min="0"
          :step="0.01"
          :precision="2"
          placeholder="0.00"
          style="width: 100%"
        />
      </el-form-item>

      <div class="form-row">
        <el-form-item label="库存单位" prop="stock_unit" style="flex: 1;">
          <el-select
            v-model="form.stock_unit"
            placeholder="选择单位"
            filterable
            allow-create
            style="width: 100%"
          >
            <el-option
              v-for="unit in commonUnits"
              :key="unit"
              :label="unit"
              :value="unit"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="是否上架" style="flex: 1; margin-left: 12px;">
          <el-switch
            v-model="form.is_listed"
            active-text="上架"
            inactive-text="下架"
          />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item label="库存数量" style="flex: 1;">
          <el-input-number
            v-model="form.stock_quantity"
            :min="0"
            :step="1"
            placeholder="0"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="最低库存" style="flex: 1; margin-left: 12px;">
          <el-input-number
            v-model="form.min_stock_level"
            :min="0"
            :step="1"
            placeholder="10"
            style="width: 100%"
          />
        </el-form-item>
      </div>

      <el-form-item label="最高库存">
        <el-input-number
          v-model="form.max_stock_level"
          :min="0"
          :step="1"
          placeholder="1000"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="商品图片">
        <el-input
          v-model="form.image_url"
          placeholder="请输入商品图片URL（可选）"
          clearable
        />
      </el-form-item>

      <el-form-item label="商品描述">
        <el-input
          v-model="form.description"
          placeholder="请输入商品描述（可选）"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="$emit('cancel')" style="flex: 1;">
        取消
      </el-button>
      <el-button 
        type="primary" 
        @click="handleSubmit"
        :loading="loading"
        style="flex: 1;"
      >
        保存修改
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { productAPI } from '@/utils/api'

const props = defineProps({
  product: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['success', 'cancel'])

const formRef = ref()
const loading = ref(false)

const form = ref({
  name: '',
  category: '',
  price: 0,
  stock_unit: '件',
  is_listed: true,
  stock_quantity: 0,
  min_stock_level: 10,
  max_stock_level: 1000,
  image_url: '',
  description: ''
})

const commonCategories = [
  '食品',
  '饮料',
  '零食',
  '日用品',
  '文具',
  '玩具',
  '服装',
  '电子产品',
  '其他'
]

const commonUnits = [
  '个',
  '件',
  '包',
  '盒',
  '瓶',
  '袋',
  '斤',
  '公斤',
  '克',
  '升',
  '毫升',
  '米',
  '厘米',
  '套',
  '双',
  '对'
]

const rules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 255, message: '商品名称长度在 2 到 255 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入商品价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  stock_unit: [
    { required: true, message: '请选择或输入库存单位', trigger: 'change' }
  ]
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
  } catch {
    return
  }

  loading.value = true
  try {
    const result = await productAPI.updateProduct(props.product.id, form.value)
    if (result.success) {
      emit('success', result.data)
    } else {
      ElMessage.error(result.error || '更新商品失败')
    }
  } catch (error) {
    ElMessage.error('更新商品失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 初始化表单数据
  form.value = {
    name: props.product.name || '',
    category: props.product.category || '',
    price: props.product.price || 0,
    stock_unit: props.product.stock_unit || '件',
    is_listed: props.product.is_listed !== undefined ? props.product.is_listed : true,
    stock_quantity: props.product.stock_quantity || 0,
    min_stock_level: props.product.min_stock_level || 10,
    max_stock_level: props.product.max_stock_level || 1000,
    image_url: props.product.image_url || '',
    description: props.product.description || ''
  }
})
</script>

<style scoped>
.product-edit-form {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-edit-form :deep(.el-form) {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20px;
}

.form-row {
  display: flex;
  align-items: flex-start;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  flex-shrink: 0;
  background: var(--bg-color);
  position: sticky;
  bottom: 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-primary);
}
</style>
