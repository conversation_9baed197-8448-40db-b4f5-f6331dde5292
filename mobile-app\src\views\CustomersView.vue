<template>
  <div class="page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索客户姓名或电话"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 筛选条件 -->
      <div class="filter-bar">
        <el-select
          v-model="orderCountFilter"
          placeholder="订单数量"
          clearable
          @change="handleFilter"
          style="width: 90px"
        >
          <el-option label="无订单" value="none" />
          <el-option label="1-5单" value="low" />
          <el-option label="6-20单" value="medium" />
          <el-option label="20+单" value="high" />
        </el-select>

        <el-select
          v-model="amountFilter"
          placeholder="消费金额"
          clearable
          @change="handleFilter"
          style="width: 90px"
        >
          <el-option label="无消费" value="none" />
          <el-option label="<100元" value="low" />
          <el-option label="100-500元" value="medium" />
          <el-option label="500+元" value="high" />
        </el-select>

        <el-select
          v-model="addressFilter"
          placeholder="地址状态"
          clearable
          @change="handleFilter"
          style="width: 90px"
        >
          <el-option label="有地址" value="has_address" />
          <el-option label="无地址" value="no_address" />
        </el-select>

        <el-select
          v-model="recentFilter"
          placeholder="活跃度"
          clearable
          @change="handleFilter"
          style="width: 80px"
        >
          <el-option label="7天内" value="week" />
          <el-option label="30天内" value="month" />
          <el-option label="90天内" value="quarter" />
          <el-option label="长期未购" value="inactive" />
        </el-select>
      </div>
    </div>

    <div class="page-content">
      <!-- 新建客户按钮 -->
      <div class="action-section">
        <el-button 
          type="primary" 
          @click="showCreateDialog = true"
          style="width: 100%"
        >
          <el-icon><Plus /></el-icon>
          新建客户
        </el-button>
      </div>

      <!-- 客户列表 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span style="margin-left: 8px;">加载中...</span>
      </div>
      
      <div v-else-if="customers.length === 0" class="empty-state">
        <el-icon><User /></el-icon>
        <div class="empty-state-text">暂无客户</div>
        <el-button type="primary" @click="showCreateDialog = true">
          创建第一个客户
        </el-button>
      </div>

      <div v-else class="customer-list">
        <div 
          v-for="customer in customers"
          :key="customer.id"
          class="customer-card card"
        >
          <div class="card-body">
            <div class="customer-header">
              <div class="customer-info">
                <div class="customer-name" @click="showCustomerDetail(customer)">
                  {{ customer.name }}
                </div>
                <div class="customer-phone">
                  <a :href="`tel:${customer.contact}`" class="phone-link">
                    {{ customer.contact }}
                  </a>
                </div>
              </div>
              <div class="customer-actions">
                <el-dropdown @command="(command) => handleAction(command, customer)">
                  <el-button type="text" :icon="MoreFilled" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="call">
                        <el-icon><Phone /></el-icon>
                        拨打电话
                      </el-dropdown-item>
                      <el-dropdown-item command="detail">
                        <el-icon><View /></el-icon>
                        查看详情
                      </el-dropdown-item>
                      <el-dropdown-item command="orders">
                        <el-icon><Document /></el-icon>
                        查看订单
                      </el-dropdown-item>
                      <el-dropdown-item command="edit" divided>编辑</el-dropdown-item>
                      <el-dropdown-item command="delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
            
            <div v-if="customer.addresses && customer.addresses.length > 0" class="customer-addresses">
              <div class="address-item" v-for="(address, index) in customer.addresses.slice(0, 2)" :key="index">
                <el-icon><Location /></el-icon>
                <span>{{ address.recipient_name }}: {{ (address.region || '') + (address.address_details || '') }}</span>
                <el-tag v-if="address.is_default" size="small" type="primary">默认</el-tag>
              </div>
              <div v-if="customer.addresses.length > 2" class="more-addresses">
                还有 {{ customer.addresses.length - 2 }} 个地址...
              </div>
            </div>

            <div v-else class="customer-address">
              <el-icon><Location /></el-icon>
              <span>暂无地址信息</span>
            </div>

            <div class="customer-stats">
              <div class="stat-item">
                <span class="stat-label">订单数</span>
                <span class="stat-value">{{ customer.order_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">消费金额</span>
                <span class="stat-value">¥{{ customer.total_amount || '0.00' }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最近订单</span>
                <span class="stat-value">
                  {{ customer.last_order_date ? formatDate(customer.last_order_date) : '无' }}
                </span>
              </div>
            </div>

            <div v-if="customer.notes" class="customer-notes">
              <div class="notes-label">备注:</div>
              <div class="notes-content">{{ customer.notes }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建客户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建客户"
      width="90%"
      :before-close="handleCloseCreate"
    >
      <CustomerCreateForm 
        @success="handleCreateSuccess"
        @cancel="showCreateDialog = false"
      />
    </el-dialog>

    <!-- 编辑客户对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑客户"
      width="90%"
      :before-close="handleCloseEdit"
    >
      <CustomerEditForm
        v-if="editingCustomer"
        :customer="editingCustomer"
        @success="handleEditSuccess"
        @cancel="showEditDialog = false"
      />
    </el-dialog>

    <!-- 客户详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="客户详情"
      width="90%"
      :before-close="handleCloseDetail"
    >
      <CustomerDetailView
        v-if="detailCustomer"
        :customer="detailCustomer"
        @close="showDetailDialog = false"
        @edit="handleEditFromDetail"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Plus,
  User,
  Loading,
  MoreFilled,
  Phone,
  Document,
  Location,
  View
} from '@element-plus/icons-vue'
import { customerAPI } from '@/utils/api'
import CustomerCreateForm from '@/components/CustomerCreateForm.vue'
import CustomerEditForm from '@/components/CustomerEditForm.vue'
import CustomerDetailView from '@/components/CustomerDetailView.vue'

const router = useRouter()
const loading = ref(false)
const customers = ref([])
const searchQuery = ref('')
const orderCountFilter = ref('')
const amountFilter = ref('')
const addressFilter = ref('')
const recentFilter = ref('')
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showDetailDialog = ref(false)
const editingCustomer = ref(null)
const detailCustomer = ref(null)

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

const loadCustomers = async () => {
  loading.value = true
  try {
    const result = await customerAPI.getCustomers()
    if (result.success) {
      let customerList = result.data || []

      // 如果有搜索条件，进行客户端过滤
      if (searchQuery.value.trim()) {
        const query = searchQuery.value.trim().toLowerCase()
        customerList = customerList.filter(customer =>
          customer.name.toLowerCase().includes(query) ||
          customer.contact.includes(query)
        )
      }

      // 应用筛选条件
      customerList = applyFilters(customerList)

      customers.value = customerList
    } else {
      ElMessage.error(result.error || '加载客户失败')
    }
  } catch (error) {
    ElMessage.error('加载客户失败')
  } finally {
    loading.value = false
  }
}

// 应用筛选条件
const applyFilters = (customerList) => {
  let filteredList = [...customerList]

  // 订单数量筛选
  if (orderCountFilter.value) {
    filteredList = filteredList.filter(customer => {
      const orderCount = customer.order_count || 0
      switch (orderCountFilter.value) {
        case 'none':
          return orderCount === 0
        case 'low':
          return orderCount >= 1 && orderCount <= 5
        case 'medium':
          return orderCount >= 6 && orderCount <= 20
        case 'high':
          return orderCount > 20
        default:
          return true
      }
    })
  }

  // 消费金额筛选
  if (amountFilter.value) {
    filteredList = filteredList.filter(customer => {
      const amount = parseFloat(customer.total_amount || 0)
      switch (amountFilter.value) {
        case 'none':
          return amount === 0
        case 'low':
          return amount > 0 && amount < 100
        case 'medium':
          return amount >= 100 && amount < 500
        case 'high':
          return amount >= 500
        default:
          return true
      }
    })
  }

  // 地址状态筛选
  if (addressFilter.value) {
    filteredList = filteredList.filter(customer => {
      const hasAddress = customer.addresses && customer.addresses.length > 0
      switch (addressFilter.value) {
        case 'has_address':
          return hasAddress
        case 'no_address':
          return !hasAddress
        default:
          return true
      }
    })
  }

  // 活跃度筛选
  if (recentFilter.value) {
    const now = new Date()
    filteredList = filteredList.filter(customer => {
      if (!customer.last_order_date) {
        return recentFilter.value === 'inactive'
      }

      const lastOrderDate = new Date(customer.last_order_date)
      const daysDiff = Math.floor((now - lastOrderDate) / (1000 * 60 * 60 * 24))

      switch (recentFilter.value) {
        case 'week':
          return daysDiff <= 7
        case 'month':
          return daysDiff <= 30
        case 'quarter':
          return daysDiff <= 90
        case 'inactive':
          return daysDiff > 90
        default:
          return true
      }
    })
  }

  return filteredList
}

const handleSearch = () => {
  loadCustomers()
}

const handleFilter = () => {
  loadCustomers()
}

const showCustomerDetail = (customer) => {
  detailCustomer.value = customer
  showDetailDialog.value = true
}

const handleEditFromDetail = (customer) => {
  // 关闭详情对话框
  showDetailDialog.value = false
  detailCustomer.value = null

  // 打开编辑对话框
  editingCustomer.value = customer
  showEditDialog.value = true
}

const handleAction = async (command, customer) => {
  if (command === 'call') {
    window.location.href = `tel:${customer.contact}`
  } else if (command === 'detail') {
    detailCustomer.value = customer
    showDetailDialog.value = true
  } else if (command === 'orders') {
    router.push(`/orders?customer_id=${customer.id}`)
  } else if (command === 'edit') {
    editingCustomer.value = customer
    showEditDialog.value = true
  } else if (command === 'delete') {
    try {
      await ElMessageBox.confirm(
        `确定要删除客户"${customer.name}"吗？此操作不可恢复。`,
        '删除客户',
        {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const result = await customerAPI.deleteCustomer(customer.id)
      if (result.success) {
        ElMessage.success('客户删除成功')
        loadCustomers()
      } else {
        ElMessage.error(result.error || '删除客户失败')
      }
    } catch {
      // 用户取消
    }
  }
}

const handleCreateSuccess = () => {
  showCreateDialog.value = false
  loadCustomers()
  ElMessage.success('客户创建成功')
}

const handleEditSuccess = () => {
  showEditDialog.value = false
  editingCustomer.value = null
  loadCustomers()
  ElMessage.success('客户更新成功')
}

const handleCloseCreate = (done) => {
  done()
}

const handleCloseEdit = (done) => {
  editingCustomer.value = null
  done()
}

const handleCloseDetail = (done) => {
  detailCustomer.value = null
  done()
}

onMounted(() => {
  loadCustomers()
})
</script>

<style scoped>
.search-section {
  background: var(--bg-color);
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.search-bar {
  margin-bottom: 12px;
}

.filter-bar {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding-bottom: 4px;
}

.action-section {
  margin-bottom: 16px;
}

.customer-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.customer-card {
  transition: transform 0.2s ease;
}

.customer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.customer-info {
  flex: 1;
  min-width: 0;
}

.customer-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 4px;
  cursor: pointer;
  transition: color 0.2s ease;
}

.customer-name:hover {
  color: var(--primary-color-dark);
  text-decoration: underline;
}

.customer-phone {
  font-size: 14px;
}

.phone-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.customer-actions {
  margin-left: 12px;
}

.customer-addresses {
  margin-bottom: 12px;
}

.address-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.address-item:last-child {
  margin-bottom: 0;
}

.more-addresses {
  font-size: 12px;
  color: var(--text-secondary);
  font-style: italic;
  margin-top: 4px;
}

.customer-address {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 12px;
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.customer-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: var(--bg-page);
  border-radius: var(--border-radius);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 11px;
  color: var(--text-secondary);
  text-align: center;
}

.stat-value {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
}

.customer-notes {
  padding-top: 12px;
  border-top: 1px solid var(--border-color);
}

.notes-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.notes-content {
  font-size: 13px;
  color: var(--text-primary);
  line-height: 1.4;
}
</style>
