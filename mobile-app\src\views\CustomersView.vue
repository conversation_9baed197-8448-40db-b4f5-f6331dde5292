<template>
  <div class="page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <el-input
        v-model="searchQuery"
        placeholder="搜索客户姓名或电话"
        clearable
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <div class="page-content">
      <!-- 新建客户按钮 -->
      <div class="action-section">
        <el-button 
          type="primary" 
          @click="showCreateDialog = true"
          style="width: 100%"
        >
          <el-icon><Plus /></el-icon>
          新建客户
        </el-button>
      </div>

      <!-- 客户列表 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span style="margin-left: 8px;">加载中...</span>
      </div>
      
      <div v-else-if="customers.length === 0" class="empty-state">
        <el-icon><User /></el-icon>
        <div class="empty-state-text">暂无客户</div>
        <el-button type="primary" @click="showCreateDialog = true">
          创建第一个客户
        </el-button>
      </div>

      <div v-else class="customer-list">
        <div 
          v-for="customer in customers"
          :key="customer.id"
          class="customer-card card"
        >
          <div class="card-body">
            <div class="customer-header">
              <div class="customer-info">
                <div class="customer-name">{{ customer.name }}</div>
                <div class="customer-phone">
                  <a :href="`tel:${customer.phone}`" class="phone-link">
                    {{ customer.phone }}
                  </a>
                </div>
              </div>
              <div class="customer-actions">
                <el-dropdown @command="(command) => handleAction(command, customer)">
                  <el-button type="text" :icon="MoreFilled" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="call">
                        <el-icon><Phone /></el-icon>
                        拨打电话
                      </el-dropdown-item>
                      <el-dropdown-item command="detail">
                        <el-icon><View /></el-icon>
                        查看详情
                      </el-dropdown-item>
                      <el-dropdown-item command="orders">
                        <el-icon><Document /></el-icon>
                        查看订单
                      </el-dropdown-item>
                      <el-dropdown-item command="edit" divided>编辑</el-dropdown-item>
                      <el-dropdown-item command="delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
            
            <div v-if="customer.addresses && customer.addresses.length > 0" class="customer-addresses">
              <div class="address-item" v-for="(address, index) in customer.addresses.slice(0, 2)" :key="index">
                <el-icon><Location /></el-icon>
                <span>{{ address.label }}: {{ address.address }}</span>
                <el-tag v-if="address.is_default" size="small" type="primary">默认</el-tag>
              </div>
              <div v-if="customer.addresses.length > 2" class="more-addresses">
                还有 {{ customer.addresses.length - 2 }} 个地址...
              </div>
            </div>

            <div v-else-if="customer.address" class="customer-address">
              <el-icon><Location /></el-icon>
              <span>{{ customer.address }}</span>
            </div>

            <div class="customer-stats">
              <div class="stat-item">
                <span class="stat-label">订单数</span>
                <span class="stat-value">{{ customer.order_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">消费金额</span>
                <span class="stat-value">¥{{ customer.total_amount || '0.00' }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最近订单</span>
                <span class="stat-value">
                  {{ customer.last_order_date ? formatDate(customer.last_order_date) : '无' }}
                </span>
              </div>
            </div>

            <div v-if="customer.notes" class="customer-notes">
              <div class="notes-label">备注:</div>
              <div class="notes-content">{{ customer.notes }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建客户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建客户"
      width="90%"
      :before-close="handleCloseCreate"
    >
      <CustomerCreateForm 
        @success="handleCreateSuccess"
        @cancel="showCreateDialog = false"
      />
    </el-dialog>

    <!-- 编辑客户对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑客户"
      width="90%"
      :before-close="handleCloseEdit"
    >
      <CustomerEditForm
        v-if="editingCustomer"
        :customer="editingCustomer"
        @success="handleEditSuccess"
        @cancel="showEditDialog = false"
      />
    </el-dialog>

    <!-- 客户详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="客户详情"
      width="90%"
      :before-close="handleCloseDetail"
    >
      <CustomerDetailView
        v-if="detailCustomer"
        :customer="detailCustomer"
        @close="showDetailDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Plus,
  User,
  Loading,
  MoreFilled,
  Phone,
  Document,
  Location,
  View
} from '@element-plus/icons-vue'
import { customerAPI } from '@/utils/api'
import CustomerCreateForm from '@/components/CustomerCreateForm.vue'
import CustomerEditForm from '@/components/CustomerEditForm.vue'
import CustomerDetailView from '@/components/CustomerDetailView.vue'

const router = useRouter()
const loading = ref(false)
const customers = ref([])
const searchQuery = ref('')
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showDetailDialog = ref(false)
const editingCustomer = ref(null)
const detailCustomer = ref(null)

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

const loadCustomers = async () => {
  loading.value = true
  try {
    const result = await customerAPI.getCustomers()
    if (result.success) {
      let customerList = result.data || []
      
      // 如果有搜索条件，进行客户端过滤
      if (searchQuery.value.trim()) {
        const query = searchQuery.value.trim().toLowerCase()
        customerList = customerList.filter(customer => 
          customer.name.toLowerCase().includes(query) ||
          customer.phone.includes(query)
        )
      }
      
      customers.value = customerList
    } else {
      ElMessage.error(result.error || '加载客户失败')
    }
  } catch (error) {
    ElMessage.error('加载客户失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  loadCustomers()
}

const handleAction = async (command, customer) => {
  if (command === 'call') {
    window.location.href = `tel:${customer.phone}`
  } else if (command === 'detail') {
    detailCustomer.value = customer
    showDetailDialog.value = true
  } else if (command === 'orders') {
    router.push(`/orders?customer_id=${customer.id}`)
  } else if (command === 'edit') {
    editingCustomer.value = customer
    showEditDialog.value = true
  } else if (command === 'delete') {
    try {
      await ElMessageBox.confirm(
        `确定要删除客户"${customer.name}"吗？此操作不可恢复。`,
        '删除客户',
        {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const result = await customerAPI.deleteCustomer(customer.id)
      if (result.success) {
        ElMessage.success('客户删除成功')
        loadCustomers()
      } else {
        ElMessage.error(result.error || '删除客户失败')
      }
    } catch {
      // 用户取消
    }
  }
}

const handleCreateSuccess = () => {
  showCreateDialog.value = false
  loadCustomers()
  ElMessage.success('客户创建成功')
}

const handleEditSuccess = () => {
  showEditDialog.value = false
  editingCustomer.value = null
  loadCustomers()
  ElMessage.success('客户更新成功')
}

const handleCloseCreate = (done) => {
  done()
}

const handleCloseEdit = (done) => {
  editingCustomer.value = null
  done()
}

const handleCloseDetail = (done) => {
  detailCustomer.value = null
  done()
}

onMounted(() => {
  loadCustomers()
})
</script>

<style scoped>
.search-section {
  background: var(--bg-color);
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.action-section {
  margin-bottom: 16px;
}

.customer-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.customer-card {
  transition: transform 0.2s ease;
}

.customer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.customer-info {
  flex: 1;
  min-width: 0;
}

.customer-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.customer-phone {
  font-size: 14px;
}

.phone-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.customer-actions {
  margin-left: 12px;
}

.customer-addresses {
  margin-bottom: 12px;
}

.address-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.address-item:last-child {
  margin-bottom: 0;
}

.more-addresses {
  font-size: 12px;
  color: var(--text-secondary);
  font-style: italic;
  margin-top: 4px;
}

.customer-address {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 12px;
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.customer-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: var(--bg-page);
  border-radius: var(--border-radius);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 11px;
  color: var(--text-secondary);
  text-align: center;
}

.stat-value {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
}

.customer-notes {
  padding-top: 12px;
  border-top: 1px solid var(--border-color);
}

.notes-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.notes-content {
  font-size: 13px;
  color: var(--text-primary);
  line-height: 1.4;
}
</style>
