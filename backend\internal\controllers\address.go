package controllers

import (
	"net/http"
	"strconv"

	"shop-order-backend/internal/models"

	"github.com/gin-gonic/gin"
	"xorm.io/xorm"
)

type AddressController struct {
	db *xorm.Engine
}

func NewAddressController(db *xorm.Engine) *AddressController {
	return &AddressController{db: db}
}

// GetAddresses 获取客户的地址列表
func (ac *AddressController) GetAddresses(c *gin.Context) {
	customerIDStr := c.Param("id")
	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{
			"error": "Invalid customer ID",
		})
		return
	}

	var addresses []models.Address
	if err := ac.db.Where("customer_id = ?", customerID).Find(&addresses); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch addresses",
		})
		return
	}

	c.J<PERSON>(http.StatusOK, gin.H{
		"data": addresses,
	})
}

// CreateAddress 创建地址
func (ac *AddressController) CreateAddress(c *gin.Context) {
	customerIDStr := c.Param("id")
	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid customer ID",
		})
		return
	}

	var req models.AddressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 检查客户是否存在
	var customer models.Customer
	has, err := ac.db.ID(customerID).Get(&customer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Customer not found",
		})
		return
	}

	// 如果设为默认地址，先取消该客户的其他默认地址
	if req.IsDefault {
		if _, err := ac.db.Table("addresses").Where("customer_id = ?", customerID).Update(map[string]interface{}{"is_default": false}); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to update existing default addresses",
			})
			return
		}
	}

	address := &models.Address{
		CustomerID:     customerID,
		RecipientName:  req.RecipientName,
		RecipientPhone: req.RecipientPhone,
		Region:         req.Region,
		AddressDetails: req.AddressDetails,
		IsDefault:      req.IsDefault,
	}

	if _, err := ac.db.Insert(address); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create address",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"data": address,
	})
}

// UpdateAddress 更新地址
func (ac *AddressController) UpdateAddress(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid address ID",
		})
		return
	}

	var req models.AddressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 检查地址是否存在
	var address models.Address
	has, err := ac.db.ID(id).Get(&address)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Address not found",
		})
		return
	}

	// 如果设为默认地址，先取消该客户的其他默认地址
	if req.IsDefault {
		if _, err := ac.db.Table("addresses").Where("customer_id = ? AND id != ?", address.CustomerID, id).Update(map[string]interface{}{"is_default": false}); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to update existing default addresses",
			})
			return
		}
	}

	// 更新地址信息
	address.RecipientName = req.RecipientName
	address.RecipientPhone = req.RecipientPhone
	address.Region = req.Region
	address.AddressDetails = req.AddressDetails
	address.IsDefault = req.IsDefault

	if _, err := ac.db.ID(id).Update(&address); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update address",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": address,
	})
}

// DeleteAddress 删除地址
func (ac *AddressController) DeleteAddress(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid address ID",
		})
		return
	}

	// 检查地址是否存在
	var address models.Address
	has, err := ac.db.ID(id).Get(&address)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Address not found",
		})
		return
	}

	// 删除地址
	if _, err := ac.db.ID(id).Delete(&models.Address{}); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete address",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Address deleted successfully",
	})
}

// SetDefaultAddress 设置默认地址
func (ac *AddressController) SetDefaultAddress(c *gin.Context) {
	customerIDStr := c.Param("id")
	customerID, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid customer ID",
		})
		return
	}

	idStr := c.Param("address_id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid address ID",
		})
		return
	}

	// 检查地址是否存在且属于该客户
	var address models.Address
	has, err := ac.db.Where("id = ? AND customer_id = ?", id, customerID).Get(&address)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Database error",
		})
		return
	}

	if !has {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Address not found",
		})
		return
	}

	// 先取消该客户的所有默认地址
	if _, err := ac.db.Table("addresses").Where("customer_id = ?", customerID).Update(map[string]interface{}{"is_default": false}); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update existing default addresses",
		})
		return
	}

	// 设置新的默认地址
	if _, err := ac.db.Table("addresses").Where("id = ?", id).Update(map[string]interface{}{"is_default": true}); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to set default address",
		})
		return
	}

	// 重新获取更新后的地址
	ac.db.ID(id).Get(&address)

	c.JSON(http.StatusOK, gin.H{
		"data": address,
	})
}
