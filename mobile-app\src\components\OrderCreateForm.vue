<template>
  <div class="order-create-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
    >
      <!-- 客户信息 -->
      <div class="form-section">
        <div class="section-title">客户信息</div>
        
        <el-form-item label="客户姓名" prop="customer_name">
          <div class="customer-input">
            <el-input
              v-model="form.customer_name"
              placeholder="请输入客户姓名"
              clearable
            />
            <el-button 
              type="primary" 
              @click="showCustomerSelect = true"
              style="margin-left: 8px;"
            >
              选择
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="联系电话" prop="customer_phone">
          <el-input
            v-model="form.customer_phone"
            placeholder="请输入联系电话"
            clearable
          />
        </el-form-item>

        <el-form-item label="收货地址">
          <el-input
            v-model="form.customer_address"
            placeholder="请输入收货地址（可选）"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
      </div>

      <!-- 订单信息 -->
      <div class="form-section">
        <div class="section-title">订单信息</div>
        
        <el-form-item label="交付日期" prop="delivery_date">
          <el-date-picker
            v-model="form.delivery_date"
            type="date"
            placeholder="选择交付日期"
            style="width: 100%"
            :disabled-date="disabledDate"
          />
        </el-form-item>

        <el-form-item label="备注信息">
          <el-input
            v-model="form.notes"
            placeholder="请输入备注信息（可选）"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </div>

      <!-- 商品清单 -->
      <div class="form-section">
        <div class="section-title">
          <span>商品清单</span>
          <el-button 
            type="primary" 
            size="small"
            @click="addItem"
          >
            添加商品
          </el-button>
        </div>

        <div v-if="form.items.length === 0" class="empty-items">
          <el-icon><Goods /></el-icon>
          <span>暂无商品，请添加商品</span>
        </div>

        <div v-else class="items-list">
          <div 
            v-for="(item, index) in form.items"
            :key="index"
            class="item-row"
          >
            <div class="item-info">
              <el-form-item 
                :prop="`items.${index}.product_name`"
                :rules="{ required: true, message: '请选择商品', trigger: 'blur' }"
              >
                <div class="product-select">
                  <el-input
                    v-model="item.product_name"
                    placeholder="商品名称"
                    readonly
                    @click="selectProduct(index)"
                  />
                  <el-button 
                    type="primary" 
                    size="small"
                    @click="selectProduct(index)"
                    style="margin-left: 8px;"
                  >
                    选择
                  </el-button>
                </div>
              </el-form-item>

              <div class="item-details">
                <el-form-item 
                  :prop="`items.${index}.quantity`"
                  :rules="{ required: true, message: '请输入数量', trigger: 'blur' }"
                >
                  <el-input-number
                    v-model="item.quantity"
                    :min="0.01"
                    :step="0.01"
                    :precision="2"
                    placeholder="数量"
                    @change="updateItemAmount(index)"
                    style="width: 100px;"
                  />
                </el-form-item>

                <span class="unit">{{ item.unit || '个' }}</span>

                <el-form-item 
                  :prop="`items.${index}.unit_price`"
                  :rules="{ required: true, message: '请输入单价', trigger: 'blur' }"
                >
                  <el-input-number
                    v-model="item.unit_price"
                    :min="0"
                    :step="0.01"
                    :precision="2"
                    placeholder="单价"
                    @change="updateItemAmount(index)"
                    style="width: 120px;"
                  />
                </el-form-item>

                <div class="item-amount">
                  ¥{{ (item.quantity * item.unit_price || 0).toFixed(2) }}
                </div>
              </div>
            </div>

            <el-button 
              type="danger" 
              size="small"
              @click="removeItem(index)"
              :icon="Delete"
            />
          </div>
        </div>

        <div v-if="form.items.length > 0" class="total-section">
          <div class="total-row">
            <span class="total-label">合计金额:</span>
            <span class="total-amount">¥{{ totalAmount.toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="$emit('cancel')" style="flex: 1;">
        取消
      </el-button>
      <el-button 
        type="primary" 
        @click="handleSubmit"
        :loading="loading"
        style="flex: 1;"
      >
        创建订单
      </el-button>
    </div>

    <!-- 客户选择对话框 -->
    <CustomerSelectDialog
      v-model="showCustomerSelect"
      @select="handleCustomerSelect"
    />

    <!-- 商品选择对话框 -->
    <ProductSelectDialog
      v-model="showProductSelect"
      @select="handleProductSelect"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Goods, Delete } from '@element-plus/icons-vue'
import { orderAPI } from '@/utils/api'
import CustomerSelectDialog from './CustomerSelectDialog.vue'
import ProductSelectDialog from './ProductSelectDialog.vue'

const emit = defineEmits(['success', 'cancel'])

const formRef = ref()
const loading = ref(false)
const showCustomerSelect = ref(false)
const showProductSelect = ref(false)
const currentItemIndex = ref(-1)

const form = ref({
  customer_name: '',
  customer_phone: '',
  customer_address: '',
  delivery_date: '',
  notes: '',
  items: []
})

const rules = {
  customer_name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ],
  customer_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  delivery_date: [
    { required: true, message: '请选择交付日期', trigger: 'change' }
  ]
}

const totalAmount = computed(() => {
  return form.value.items.reduce((total, item) => {
    return total + (item.quantity * item.unit_price || 0)
  }, 0)
})

const disabledDate = (time) => {
  // 禁用过去的日期
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

const addItem = () => {
  form.value.items.push({
    product_name: '',
    product_id: '',
    quantity: 1,
    unit: '个',
    unit_price: 0
  })
}

const removeItem = (index) => {
  form.value.items.splice(index, 1)
}

const selectProduct = (index) => {
  currentItemIndex.value = index
  showProductSelect.value = true
}

const updateItemAmount = (index) => {
  // 触发响应式更新
  const item = form.value.items[index]
  if (item.quantity && item.unit_price) {
    // 强制更新
    form.value.items[index] = { ...item }
  }
}

const handleCustomerSelect = (customer) => {
  form.value.customer_name = customer.name
  form.value.customer_phone = customer.phone
  form.value.customer_address = customer.address || ''
}

const handleProductSelect = (product) => {
  if (currentItemIndex.value >= 0) {
    const item = form.value.items[currentItemIndex.value]
    item.product_name = product.name
    item.product_id = product.id
    item.unit = product.unit
    item.unit_price = product.price
    
    // 强制更新
    form.value.items[currentItemIndex.value] = { ...item }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
  } catch {
    return
  }

  if (form.value.items.length === 0) {
    ElMessage.warning('请至少添加一个商品')
    return
  }

  loading.value = true
  try {
    const orderData = {
      ...form.value,
      total_amount: totalAmount.value,
      status: 'pending'
    }

    const result = await orderAPI.createOrder(orderData)
    if (result.success) {
      emit('success')
    } else {
      ElMessage.error(result.error || '创建订单失败')
    }
  } catch (error) {
    ElMessage.error('创建订单失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.order-create-form {
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customer-input {
  display: flex;
  align-items: center;
}

.customer-input .el-input {
  flex: 1;
}

.empty-items {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: var(--text-secondary);
  font-size: 14px;
}

.empty-items .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.item-row {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.item-info {
  flex: 1;
  min-width: 0;
}

.product-select {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.product-select .el-input {
  flex: 1;
  cursor: pointer;
}

.item-details {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.unit {
  font-size: 14px;
  color: var(--text-secondary);
  white-space: nowrap;
}

.item-amount {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
  margin-left: auto;
}

.total-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 2px solid var(--border-color);
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
}

.total-label {
  color: var(--text-primary);
}

.total-amount {
  color: var(--primary-color);
  font-size: 18px;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-primary);
}
</style>
