<template>
  <div class="product-create-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="商品名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入商品名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="商品分类">
        <el-select
          v-model="form.category"
          placeholder="请选择或输入分类"
          filterable
          allow-create
          style="width: 100%"
        >
          <el-option
            v-for="category in commonCategories"
            :key="category"
            :label="category"
            :value="category"
          />
        </el-select>
      </el-form-item>

      <div class="form-row">
        <el-form-item label="售价" prop="price" style="flex: 1;">
          <el-input-number
            v-model="form.price"
            :min="0"
            :step="0.01"
            :precision="2"
            placeholder="0.00"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="成本" style="flex: 1; margin-left: 12px;">
          <el-input-number
            v-model="form.cost"
            :min="0"
            :step="0.01"
            :precision="2"
            placeholder="0.00"
            style="width: 100%"
          />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item label="单位" prop="unit" style="flex: 1;">
          <el-select
            v-model="form.unit"
            placeholder="选择单位"
            filterable
            allow-create
            style="width: 100%"
          >
            <el-option
              v-for="unit in commonUnits"
              :key="unit"
              :label="unit"
              :value="unit"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="最低库存" style="flex: 1; margin-left: 12px;">
          <el-input-number
            v-model="form.min_stock"
            :min="0"
            :step="1"
            placeholder="10"
            style="width: 100%"
          />
        </el-form-item>
      </div>

      <el-form-item label="库存数量">
        <el-input-number
          v-model="form.stock"
          :min="0"
          :step="1"
          placeholder="0"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="商品描述">
        <el-input
          v-model="form.description"
          placeholder="请输入商品描述（可选）"
          type="textarea"
          :rows="3"
        />
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="$emit('cancel')" style="flex: 1;">
        取消
      </el-button>
      <el-button 
        type="primary" 
        @click="handleSubmit"
        :loading="loading"
        style="flex: 1;"
      >
        创建商品
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { productAPI } from '@/utils/api'

const emit = defineEmits(['success', 'cancel'])

const formRef = ref()
const loading = ref(false)

const form = ref({
  name: '',
  category: '',
  price: 0,
  cost: 0,
  unit: '个',
  stock: 0,
  min_stock: 10,
  description: ''
})

const commonCategories = [
  '食品',
  '饮料',
  '零食',
  '日用品',
  '文具',
  '玩具',
  '服装',
  '电子产品',
  '其他'
]

const commonUnits = [
  '个',
  '件',
  '包',
  '盒',
  '瓶',
  '袋',
  '斤',
  '公斤',
  '克',
  '升',
  '毫升',
  '米',
  '厘米',
  '套',
  '双',
  '对'
]

const rules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '商品名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入商品单价', trigger: 'blur' },
    { type: 'number', min: 0, message: '单价不能小于0', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请选择或输入单位', trigger: 'change' }
  ]
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
  } catch {
    return
  }

  loading.value = true
  try {
    const result = await productAPI.createProduct(form.value)
    if (result.success) {
      emit('success', result.data)
    } else {
      ElMessage.error(result.error || '创建商品失败')
    }
  } catch (error) {
    ElMessage.error('创建商品失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.product-create-form {
  max-height: 60vh;
  overflow-y: auto;
}

.form-row {
  display: flex;
  align-items: flex-start;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-primary);
}
</style>
