package controllers

import (
	"net/http"
	"strconv"

	"shop-order-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/robfig/cron/v3"
)

// SchedulerController 定时任务控制器
type SchedulerController struct {
	schedulerService *services.SchedulerService
}

// NewSchedulerController 创建定时任务控制器
func NewSchedulerController(schedulerService *services.SchedulerService) *SchedulerController {
	return &SchedulerController{
		schedulerService: schedulerService,
	}
}

// GetStatus 获取定时任务状态
func (sc *SchedulerController) GetStatus(c *gin.Context) {
	status := sc.schedulerService.GetJobStatus()
	c.JSO<PERSON>(http.StatusOK, gin.H{
		"data":    status,
		"success": true,
	})
}

// RunJob 立即执行指定任务
func (sc *SchedulerController) RunJob(c *gin.Context) {
	jobName := c.<PERSON>m("job")
	if jobName == "" {
		c.<PERSON><PERSON><PERSON>(http.StatusBadRequest, gin.H{
			"error": "Job name is required",
		})
		return
	}

	err := sc.schedulerService.RunJobNow(jobName)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Job executed successfully",
		"job":     jobName,
		"success": true,
	})
}

// GetJobs 获取所有定时任务列表
func (sc *SchedulerController) GetJobs(c *gin.Context) {
	jobs := sc.schedulerService.GetJobs()

	var jobList []map[string]interface{}
	for i, job := range jobs {
		jobInfo := map[string]interface{}{
			"id":       job.ID,
			"next_run": job.Next,
			"prev_run": job.Prev,
		}

		// 根据索引推断任务名称
		switch i {
		case 0:
			jobInfo["name"] = "sync_current_year_holidays"
			jobInfo["description"] = "同步当年节假日数据"
			jobInfo["schedule"] = "每天凌晨2点"
		case 1:
			jobInfo["name"] = "sync_next_year_holidays"
			jobInfo["description"] = "同步下一年节假日数据"
			jobInfo["schedule"] = "每年1月1日凌晨3点"
		case 2:
			jobInfo["name"] = "cleanup_expired_data"
			jobInfo["description"] = "清理过期数据"
			jobInfo["schedule"] = "每小时"
		case 3:
			jobInfo["name"] = "generate_daily_stats"
			jobInfo["description"] = "生成每日统计"
			jobInfo["schedule"] = "每天凌晨1点"
		case 4:
			jobInfo["name"] = "sync_lunar_calendar"
			jobInfo["description"] = "同步皇历数据（可选）"
			jobInfo["schedule"] = "每天凌晨4点"
		default:
			jobInfo["name"] = "custom_job"
			jobInfo["description"] = "自定义任务"
			jobInfo["schedule"] = "未知"
		}

		jobList = append(jobList, jobInfo)
	}

	c.JSON(http.StatusOK, gin.H{
		"data":    jobList,
		"count":   len(jobList),
		"success": true,
	})
}

// RemoveJob 移除定时任务
func (sc *SchedulerController) RemoveJob(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid job ID",
		})
		return
	}

	sc.schedulerService.RemoveJob(cron.EntryID(id))
	c.JSON(http.StatusOK, gin.H{
		"message": "Job removed successfully",
		"success": true,
	})
}

// AddCustomJob 添加自定义定时任务
func (sc *SchedulerController) AddCustomJob(c *gin.Context) {
	var req struct {
		Spec        string `json:"spec" binding:"required"`
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 这里可以根据需要添加自定义任务逻辑
	// 目前只是一个示例
	_, err := sc.schedulerService.AddCustomJob(req.Spec, func() {
		// 自定义任务逻辑
	})

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to add custom job",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Custom job added successfully",
		"success": true,
	})
}
