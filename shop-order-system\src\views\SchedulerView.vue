<template>
  <div class="scheduler-view">
    <div class="page-header">
      <h2>定时任务管理</h2>
      <el-button type="primary" @click="refreshData" :loading="loading">
        <el-icon><Refresh /></el-icon>
        刷新状态
      </el-button>
    </div>

    <!-- 系统状态卡片 -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon running">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-title">定时任务状态</div>
              <div class="status-value">{{ schedulerStatus.is_running ? '运行中' : '已停止' }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon">
              <el-icon><List /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-title">活跃任务数</div>
              <div class="status-value">{{ jobs.length }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-title">运行时长</div>
              <div class="status-value">{{ formatUptime(schedulerStatus.uptime) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-title">下次执行</div>
              <div class="status-value">{{ getNextRunTime() }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务列表 -->
    <el-card class="jobs-card">
      <template #header>
        <div class="card-header">
          <span>定时任务列表</span>
          <div class="header-buttons">
            <el-button type="success" size="small" @click="syncHolidays">
              <el-icon><Download /></el-icon>
              立即同步节假日
            </el-button>
            <el-button type="danger" size="small" @click="syncLunarCalendar">
              <el-icon><Calendar /></el-icon>
              立即同步皇历
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="jobs" style="width: 100%" v-loading="loading">
        <el-table-column prop="name" label="任务名称" width="200">
          <template #default="scope">
            <div class="job-name">
              <el-tag :type="getJobTagType(scope.row.name)" size="small">
                {{ scope.row.name }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="任务描述" min-width="200" />
        <el-table-column prop="schedule" label="执行计划" width="150" />
        <el-table-column label="下次执行时间" width="180">
          <template #default="scope">
            <span v-if="scope.row.next_run">
              {{ formatDateTime(scope.row.next_run) }}
            </span>
            <span v-else class="text-muted">未设置</span>
          </template>
        </el-table-column>
        <el-table-column label="上次执行时间" width="180">
          <template #default="scope">
            <span v-if="scope.row.prev_run && scope.row.prev_run !== '0001-01-01T00:00:00Z'">
              {{ formatDateTime(scope.row.prev_run) }}
            </span>
            <span v-else class="text-muted">从未执行</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="runJobNow(scope.row)"
              :loading="runningJobs.includes(scope.row.name)"
            >
              立即执行
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 执行日志 -->
    <el-card class="logs-card">
      <template #header>
        <div class="card-header">
          <span>执行日志</span>
          <el-button type="text" size="small" @click="clearLogs">清空日志</el-button>
        </div>
      </template>

      <div class="logs-container">
        <div v-if="logs.length === 0" class="empty-logs">
          <el-empty description="暂无执行日志" />
        </div>
        <div v-else class="logs-list">
          <div
            v-for="(log, index) in logs"
            :key="index"
            class="log-item"
            :class="{ 'log-error': log.type === 'error', 'log-success': log.type === 'success' }"
          >
            <div class="log-time">{{ formatDateTime(log.time) }}</div>
            <div class="log-content">{{ log.message }}</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Timer, List, Clock, Calendar, Download } from '@element-plus/icons-vue'
import { schedulerAPI } from '../lib/api'

// 状态
const loading = ref(false)
const schedulerStatus = reactive({
  is_running: false,
  uptime: 0,
  next_runs: []
})
const jobs = ref([])
const logs = ref([])
const runningJobs = ref([])

// 定时刷新
let refreshInterval = null

// 方法
const refreshData = async () => {
  try {
    loading.value = true
    
    // 获取定时任务状态
    const statusResult = await schedulerAPI.getStatus()
    if (statusResult.success) {
      Object.assign(schedulerStatus, statusResult.data)
    }

    // 获取任务列表
    const jobsResult = await schedulerAPI.getJobs()
    if (jobsResult.success) {
      jobs.value = jobsResult.data
    }
  } catch (error) {
    console.error('获取定时任务数据失败:', error)
    ElMessage.error('获取定时任务数据失败')
  } finally {
    loading.value = false
  }
}

const runJobNow = async (job) => {
  try {
    runningJobs.value.push(job.name)
    
    const result = await schedulerAPI.runJob(job.name)
    if (result.success) {
      ElMessage.success(`任务 ${job.name} 执行成功`)
      addLog('success', `手动执行任务: ${job.description}`)
      
      // 刷新数据
      setTimeout(refreshData, 1000)
    }
  } catch (error) {
    console.error('执行任务失败:', error)
    ElMessage.error('执行任务失败')
    addLog('error', `执行任务失败: ${job.description} - ${error.message}`)
  } finally {
    runningJobs.value = runningJobs.value.filter(name => name !== job.name)
  }
}

const syncHolidays = async () => {
  const currentYear = new Date().getFullYear()
  try {
    loading.value = true

    const result = await schedulerAPI.runJob('sync_current_year_holidays')
    if (result.success) {
      ElMessage.success(`${currentYear}年节假日数据同步成功`)
      addLog('success', `同步${currentYear}年节假日数据`)
    }
  } catch (error) {
    console.error('同步节假日失败:', error)
    ElMessage.error('同步节假日失败')
    addLog('error', `同步节假日失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

const syncLunarCalendar = async () => {
  try {
    loading.value = true

    const result = await schedulerAPI.runJob('sync_lunar_calendar')
    if (result.success) {
      ElMessage.success('皇历数据同步成功')
      addLog('success', '同步皇历数据（黄道吉日、宜忌事项等）')
    }
  } catch (error) {
    console.error('同步皇历失败:', error)
    ElMessage.error('同步皇历失败')
    addLog('error', `同步皇历失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

const addLog = (type, message) => {
  logs.value.unshift({
    type,
    message,
    time: new Date().toISOString()
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

const clearLogs = () => {
  logs.value = []
  ElMessage.success('日志已清空')
}

const formatDateTime = (dateString) => {
  if (!dateString || dateString === '0001-01-01T00:00:00Z') return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatUptime = (seconds) => {
  if (!seconds) return '0秒'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

const getNextRunTime = () => {
  if (!schedulerStatus.next_runs || schedulerStatus.next_runs.length === 0) {
    return '无'
  }
  
  const nextRun = schedulerStatus.next_runs[0]
  return formatDateTime(nextRun.next_run)
}

const getJobTagType = (jobName) => {
  const typeMap = {
    'sync_current_year_holidays': 'success',
    'sync_next_year_holidays': 'info',
    'cleanup_expired_data': 'warning',
    'generate_daily_stats': 'primary',
    'sync_lunar_calendar': 'danger'
  }
  return typeMap[jobName] || 'default'
}

// 生命周期
onMounted(() => {
  refreshData()
  
  // 每30秒自动刷新
  refreshInterval = setInterval(refreshData, 30000)
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>

<style scoped>
.scheduler-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-cards {
  margin-bottom: 20px;
}

.status-card {
  height: 100px;
}

.status-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f2f5;
  margin-right: 15px;
  font-size: 20px;
  color: #666;
}

.status-icon.running {
  background-color: #e7f7ff;
  color: #1890ff;
}

.status-content {
  flex: 1;
}

.status-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.jobs-card,
.logs-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

.job-name {
  display: flex;
  align-items: center;
}

.logs-container {
  max-height: 400px;
  overflow-y: auto;
}

.logs-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.log-item {
  display: flex;
  padding: 10px;
  border-radius: 4px;
  background-color: #f8f9fa;
  border-left: 3px solid #ddd;
}

.log-item.log-success {
  background-color: #f6ffed;
  border-left-color: #52c41a;
}

.log-item.log-error {
  background-color: #fff2f0;
  border-left-color: #ff4d4f;
}

.log-time {
  width: 150px;
  font-size: 12px;
  color: #666;
  flex-shrink: 0;
}

.log-content {
  flex: 1;
  font-size: 14px;
}

.text-muted {
  color: #999;
}

.empty-logs {
  text-align: center;
  padding: 40px 0;
}
</style>
