// 移动端API配置和工具函数
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api'

// 获取认证token
const getAuthToken = () => {
  return localStorage.getItem('auth_token')
}

// 设置认证token
const setAuthToken = (token) => {
  if (token) {
    localStorage.setItem('auth_token', token)
  } else {
    localStorage.removeItem('auth_token')
  }
}

// 通用请求函数
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`
  const token = getAuthToken()
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  }

  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }

  try {
    const response = await fetch(url, config)
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `请求失败，状态码: ${response.status}`)
    }

    const data = await response.json()
    return { success: true, data: data.data || data }
  } catch (error) {
    console.error('API请求失败:', error)
    return { success: false, error: error.message }
  }
}

// 认证相关API
export const authAPI = {
  // 登录
  login: async (email, password) => {
    return apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    })
  },

  // 获取用户信息
  getProfile: async () => {
    return apiRequest('/user/profile')
  }
}

// 订单相关API
export const orderAPI = {
  // 获取订单列表
  getOrders: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = queryString ? `/orders?${queryString}` : '/orders'
    return apiRequest(endpoint)
  },

  // 获取单个订单
  getOrder: async (id) => {
    return apiRequest(`/orders/${id}`)
  },

  // 创建订单
  createOrder: async (orderData) => {
    return apiRequest('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    })
  },

  // 更新订单
  updateOrder: async (id, orderData) => {
    return apiRequest(`/orders/${id}`, {
      method: 'PUT',
      body: JSON.stringify(orderData),
    })
  },

  // 删除订单
  deleteOrder: async (id) => {
    return apiRequest(`/orders/${id}`, {
      method: 'DELETE',
    })
  },

  // 获取每日订单汇总
  getDailySummary: async (date) => {
    return apiRequest(`/orders/summary/daily/${date}`)
  }
}

// 商品相关API
export const productAPI = {
  // 获取商品列表
  getProducts: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = queryString ? `/products?${queryString}` : '/products'
    return apiRequest(endpoint)
  },

  // 获取单个商品
  getProduct: async (id) => {
    return apiRequest(`/products/${id}`)
  },

  // 创建商品
  createProduct: async (productData) => {
    return apiRequest('/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    })
  },

  // 更新商品
  updateProduct: async (id, productData) => {
    return apiRequest(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(productData),
    })
  },

  // 删除商品
  deleteProduct: async (id) => {
    return apiRequest(`/products/${id}`, {
      method: 'DELETE',
    })
  }
}

// 客户相关API
export const customerAPI = {
  // 获取客户列表
  getCustomers: async () => {
    return apiRequest('/customers')
  },

  // 获取单个客户
  getCustomer: async (id) => {
    return apiRequest(`/customers/${id}`)
  },

  // 创建客户
  createCustomer: async (customerData) => {
    return apiRequest('/customers', {
      method: 'POST',
      body: JSON.stringify(customerData),
    })
  },

  // 更新客户
  updateCustomer: async (id, customerData) => {
    return apiRequest(`/customers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(customerData),
    })
  },

  // 删除客户
  deleteCustomer: async (id) => {
    return apiRequest(`/customers/${id}`, {
      method: 'DELETE',
    })
  }
}

// 日历相关API
export const calendarAPI = {
  // 获取日历信息
  getCalendarInfo: async (date) => {
    return apiRequest(`/calendar/info?date=${date}`)
  },

  // 获取月度日历
  getMonthCalendar: async (year, month) => {
    try {
      // 模拟日历数据，包含农历和节假日信息
      const mockData = generateMockCalendarData(year, month)
      return { success: true, data: mockData }
    } catch (error) {
      return { success: false, error: error.message }
    }
  },

  // 获取节假日信息
  getHolidays: async (year) => {
    try {
      const mockHolidays = generateMockHolidays(year)
      return { success: true, data: mockHolidays }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }
}

// 生成模拟日历数据
function generateMockCalendarData(year, month) {
  const data = {}
  const daysInMonth = new Date(year, month, 0).getDate()

  // 农历月份名称
  const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月',
                      '七月', '八月', '九月', '十月', '冬月', '腊月']

  // 农历日期名称
  const lunarDays = ['初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
                    '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
                    '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十']

  // 节假日数据
  const holidays = {
    '01-01': '元旦',
    '02-14': '情人节',
    '03-08': '妇女节',
    '04-05': '清明节',
    '05-01': '劳动节',
    '06-01': '儿童节',
    '10-01': '国庆节',
    '12-25': '圣诞节'
  }

  for (let day = 1; day <= daysInMonth; day++) {
    const dateString = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
    const monthDay = `${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`

    // 生成农历信息
    const lunarDay = (day % 30) + 1
    const lunarMonth = Math.floor((day - 1) / 30) % 12
    const lunar = lunarDay === 1 ? lunarMonths[lunarMonth] : lunarDays[lunarDay - 1]

    // 检查是否是节假日
    const holiday = holidays[monthDay]

    // 生成黄历信息
    const almanac = generateAlmanacInfo(day)

    data[dateString] = {
      lunar,
      holiday,
      almanac
    }
  }

  return data
}

// 生成黄历信息
function generateAlmanacInfo(day) {
  const suitable = ['祭祀', '祈福', '开业', '出行', '嫁娶', '动土', '安床', '作灶']
  const avoid = ['破土', '安葬', '开仓', '出货财', '启攒', '修造', '动土']

  const suitableItems = suitable.slice(0, (day % 4) + 2)
  const avoidItems = avoid.slice(0, (day % 3) + 1)

  return `宜: ${suitableItems.join('、')} 忌: ${avoidItems.join('、')}`
}

// 生成模拟节假日数据
function generateMockHolidays(year) {
  return [
    { date: `${year}-01-01`, name: '元旦', type: 'holiday' },
    { date: `${year}-02-14`, name: '情人节', type: 'festival' },
    { date: `${year}-03-08`, name: '妇女节', type: 'holiday' },
    { date: `${year}-04-05`, name: '清明节', type: 'holiday' },
    { date: `${year}-05-01`, name: '劳动节', type: 'holiday' },
    { date: `${year}-06-01`, name: '儿童节', type: 'holiday' },
    { date: `${year}-10-01`, name: '国庆节', type: 'holiday' },
    { date: `${year}-12-25`, name: '圣诞节', type: 'festival' }
  ]
}

// 工具函数
export { getAuthToken, setAuthToken }
