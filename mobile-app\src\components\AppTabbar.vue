<template>
  <div class="app-tabbar">
    <div 
      v-for="item in tabItems"
      :key="item.path"
      @click="navigateTo(item.path)"
      class="tab-item"
      :class="{ active: isActive(item.path) }"
    >
      <el-icon :size="20">
        <component :is="item.icon" />
      </el-icon>
      <span class="tab-label">{{ item.label }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Odometer, 
  Document, 
  Calendar, 
  Goods, 
  User 
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const tabItems = [
  { path: '/', label: '首页', icon: 'Odometer' },
  { path: '/orders', label: '订单', icon: 'Document' },
  { path: '/calendar', label: '日历', icon: 'Calendar' },
  { path: '/products', label: '商品', icon: 'Goods' },
  { path: '/customers', label: '客户', icon: 'User' }
]

const isActive = (path) => {
  if (path === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(path)
}

const navigateTo = (path) => {
  if (route.path !== path) {
    router.push(path)
  }
}
</script>

<style scoped>
.app-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--tabbar-height);
  background: var(--bg-color);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 100;
  padding-bottom: var(--safe-area-bottom, 0);
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 12px;
  cursor: pointer;
  transition: color 0.2s ease;
  color: var(--text-secondary);
  min-width: 50px;
}

.tab-item.active {
  color: var(--primary-color);
}

.tab-label {
  font-size: 10px;
  font-weight: 500;
  line-height: 1;
}

/* 安全区域适配 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .app-tabbar {
    height: calc(var(--tabbar-height) + env(safe-area-inset-bottom));
    padding-bottom: env(safe-area-inset-bottom);
  }
}
</style>
