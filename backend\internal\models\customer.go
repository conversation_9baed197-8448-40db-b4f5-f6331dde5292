package models

import (
	"time"
)

// Customer 客户模型
type Customer struct {
	ID        int64     `json:"id" xorm:"pk autoincr 'id'"`
	Name      string    `json:"name" xorm:"varchar(255) notnull 'name'"`
	Contact   string    `json:"contact" xorm:"varchar(255) 'contact'"`
	CreatedAt time.Time `json:"created_at" xorm:"created 'created_at'"`
	UpdatedAt time.Time `json:"updated_at" xorm:"updated 'updated_at'"`

	// 关联地址（不存储在数据库中）
	Addresses []*Address `json:"addresses,omitempty" xorm:"-"`
	// 地址数量（不存储在数据库中）
	AddressCount int `json:"address_count,omitempty" xorm:"-"`
}

// TableName 指定表名
func (Customer) TableName() string {
	return "customers"
}

// Address 地址模型
type Address struct {
	ID             int64     `json:"id" xorm:"pk autoincr 'id'"`
	CustomerID     int64     `json:"customer_id" xorm:"bigint notnull 'customer_id'"`
	RecipientName  string    `json:"recipient_name" xorm:"varchar(255) 'recipient_name'"`
	RecipientPhone string    `json:"recipient_phone" xorm:"varchar(255) 'recipient_phone'"`
	Region         string    `json:"region" xorm:"varchar(255) 'region'"`
	AddressDetails string    `json:"address_details" xorm:"text 'address_details'"`
	IsDefault      bool      `json:"is_default" xorm:"bool notnull default(false) 'is_default'"`
	CreatedAt      time.Time `json:"created_at" xorm:"created 'created_at'"`

	// 关联客户（不存储在数据库中）
	Customer *Customer `json:"customer,omitempty" xorm:"-"`
}

// TableName 指定表名
func (Address) TableName() string {
	return "addresses"
}

// CustomerRequest 客户请求
type CustomerRequest struct {
	Name    string `json:"name" binding:"required"`
	Contact string `json:"contact"`
}

// AddressRequest 地址请求
type AddressRequest struct {
	RecipientName  string `json:"recipient_name" binding:"required"`
	RecipientPhone string `json:"recipient_phone" binding:"required"`
	Region         string `json:"region" binding:"required"`
	AddressDetails string `json:"address_details" binding:"required"`
	IsDefault      bool   `json:"is_default"`
}
