<template>
  <div class="app-header">
    <div class="header-content">
      <!-- 左侧返回按钮或标题 -->
      <div class="header-left">
        <el-button 
          v-if="showBack"
          @click="goBack"
          type="text"
          class="back-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <h1 class="header-title">{{ title }}</h1>
      </div>
      
      <!-- 右侧操作按钮 -->
      <div class="header-right">
        <slot name="right">
          <el-dropdown @command="handleCommand" trigger="click">
            <el-button type="text" class="user-btn">
              <el-icon><User /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { ArrowLeft, User } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

const title = computed(() => route.meta.title || '小商店')
const showBack = computed(() => {
  // 在首页、订单、日历、商品、客户页面不显示返回按钮
  const noBackPages = ['/', '/orders', '/calendar', '/products', '/customers']
  return !noBackPages.includes(route.path)
})

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      // 这里添加退出登录逻辑
      localStorage.removeItem('auth_token')
      router.push('/login')
    } catch {
      // 用户取消
    }
  }
}
</script>

<style scoped>
.app-header {
  height: var(--header-height);
  background: var(--bg-color);
  border-bottom: 1px solid var(--border-color);
  position: relative;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 16px;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.back-btn {
  padding: 8px;
  margin-right: 8px;
  color: var(--text-primary);
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-btn {
  padding: 8px;
  color: var(--text-primary);
}
</style>
