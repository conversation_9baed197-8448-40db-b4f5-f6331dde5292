<template>
  <div class="calendar-view">
    <div class="page-header">
      <h2>生产/配送日历</h2>
      <div class="header-controls">
        <div class="date-navigation">
          <el-button @click="navigateDate('prev')" :icon="ArrowLeft" circle />
          <span class="current-date-display">{{ formatCurrentDate() }}</span>
          <el-button @click="navigateDate('next')" :icon="ArrowRight" circle />
          <el-button @click="goToToday" type="primary" size="small">今天</el-button>
        </div>
        <div class="view-controls">
          <el-radio-group v-model="viewMode">
            <el-radio-button label="month">月视图</el-radio-button>
            <el-radio-button label="week">周视图</el-radio-button>
            <el-radio-button label="year">年视图</el-radio-button>
          </el-radio-group>
          <el-switch
            v-model="showLunarInfo"
            active-text="显示皇历"
            inactive-text="隐藏皇历"
            style="margin-left: 12px;"
          />
          <el-button @click="refreshData" :icon="Refresh" :loading="loading" circle />
        </div>
      </div>
    </div>

    <el-card>
      <!-- 快捷键提示 -->
      <div class="keyboard-shortcuts" v-if="showShortcuts">
        <el-alert
          title="快捷键提示"
          type="info"
          :closable="true"
          @close="showShortcuts = false"
        >
          <template #default>
            <div class="shortcuts-grid">
              <div class="shortcut-item">
                <kbd>←</kbd> <kbd>→</kbd> 切换月份/年份
              </div>
              <div class="shortcut-item">
                <kbd>T</kbd> 回到今天
              </div>
              <div class="shortcut-item">
                <kbd>R</kbd> 刷新数据
              </div>
              <div class="shortcut-item">
                <kbd>1</kbd> <kbd>2</kbd> <kbd>3</kbd> 切换视图
              </div>
            </div>
          </template>
        </el-alert>
      </div>



      <div class="calendar-container" :class="{ loading }">
        <!-- 月视图 -->
        <el-calendar
          v-if="viewMode === 'month'"
          v-model="currentDate"
          v-loading="loading"
        >
          <template #date-cell="{ data }">
            <div
              class="calendar-cell"
              @click="handleDateClick(data)"
              @dblclick="handleDateDoubleClick(data)"
              :class="{
                'has-orders': getOrdersForDate(data.day).length > 0,
                'is-holiday': isHoliday(data.day),
                'is-weekend': isWeekend(data.day),
                'is-good-day': showLunarInfo && isGoodDay(data.day),
                'is-other-month': data.type === 'prev-month' || data.type === 'next-month'
              }"
            >
              <div class="date-info">
                <span class="solar-date">{{ data.day.split('-')[2] }}</span>
                <span class="lunar-date">{{ getLunarDate(data.day) }}</span>
              </div>

              <!-- 黄历信息 -->
              <div v-if="showLunarInfo" class="huangli-info">
                <!-- 第一优先级：节气 -->
                <div v-if="getHuangLiInfo(data.day).jieQi" class="special-info jieqi">
                  <span class="special-tag jieqi-tag">{{ getHuangLiInfo(data.day).jieQi }}</span>
                </div>
                <!-- 第二优先级：节日 -->
                <div v-else-if="getHuangLiInfo(data.day).festivals.length > 0" class="special-info festival">
                  <span class="special-tag festival-tag">{{ getHuangLiInfo(data.day).festivals[0] }}</span>
                </div>
                <!-- 第三优先级：宜忌信息 -->
                <div v-else class="yiji-container">
                  <div v-if="getHuangLiInfo(data.day).yi.length > 0" class="yiji-item yi-item">
                    <span class="yiji-label yi-label">宜</span>
                    <span class="yiji-content">{{ getHuangLiInfo(data.day).yi.slice(0, 2).join(' ') }}</span>
                  </div>
                  <div v-if="getHuangLiInfo(data.day).ji.length > 0" class="yiji-item ji-item">
                    <span class="yiji-label ji-label">忌</span>
                    <span class="yiji-content">{{ getHuangLiInfo(data.day).ji.slice(0, 2).join(' ') }}</span>
                  </div>
                </div>
                <!-- 建除十二值星（小标记） -->
                <div v-if="getHuangLiInfo(data.day).zhiXing" class="zhixing-mark">
                  <span class="zhixing-badge">{{ getHuangLiInfo(data.day).zhiXing }}</span>
                </div>
              </div>

              <div v-if="getOrdersForDate(data.day).length > 0" class="order-info">
                <el-tag size="small" type="primary">
                  {{ getOrdersForDate(data.day).length }}单
                </el-tag>
                <div class="product-summary">
                  <el-tag
                    v-for="summary in getProductSummaryForDate(data.day)"
                    :key="summary.category"
                    size="small"
                    type="info"
                    class="category-tag"
                  >
                    {{ summary.category }}: {{ summary.quantity }}
                  </el-tag>
                </div>
              </div>
              <div v-if="isHoliday(data.day)" class="holiday-mark">
                <el-tag size="small" type="danger">休</el-tag>
              </div>

            </div>
          </template>
        </el-calendar>

        <!-- 周视图 -->
        <div v-else-if="viewMode === 'week'" class="week-view" v-loading="loading">
          <div class="week-header">
            <div class="week-title">{{ getWeekTitle() }}</div>
          </div>
          <div class="week-grid">
            <div class="week-days-header">
              <div v-for="day in weekDays" :key="day" class="week-day-header">
                {{ day }}
              </div>
            </div>
            <div class="week-days-content">
              <div
                v-for="date in getWeekDates()"
                :key="date.dateStr"
                class="week-day-cell"
                @click="handleWeekDateClick(date)"
                :class="{
                  'has-orders': getOrdersForDate(date.dateStr).length > 0,
                  'is-holiday': isHoliday(date.dateStr),
                  'is-weekend': isWeekend(date.dateStr),
                  'is-today': isToday(date.dateStr),
                  'is-good-day': showLunarInfo && isGoodDay(date.dateStr),
                  'is-other-week': !isInCurrentWeek(date.dateStr)
                }"
              >
                <div class="week-date-info">
                  <span class="week-solar-date">{{ date.day }}</span>
                  <span class="week-lunar-date">{{ getLunarDate(date.dateStr) }}</span>
                </div>

                <!-- 周视图黄历信息 -->
                <div v-if="showLunarInfo" class="week-huangli-info">
                  <!-- 第一优先级：节气 -->
                  <div v-if="getHuangLiInfo(date.dateStr).jieQi" class="week-special-info week-jieqi">
                    <span class="week-special-tag week-jieqi-tag">{{ getHuangLiInfo(date.dateStr).jieQi }}</span>
                  </div>
                  <!-- 第二优先级：节日 -->
                  <div v-else-if="getHuangLiInfo(date.dateStr).festivals.length > 0" class="week-special-info week-festival">
                    <span class="week-special-tag week-festival-tag">{{ getHuangLiInfo(date.dateStr).festivals[0] }}</span>
                  </div>
                  <!-- 第三优先级：宜忌信息 -->
                  <div v-else class="week-yiji-container">
                    <div v-if="getHuangLiInfo(date.dateStr).yi.length > 0" class="week-yiji-item week-yi-item">
                      <span class="week-yiji-label week-yi-label">宜</span>
                      <span class="week-yiji-content">{{ getHuangLiInfo(date.dateStr).yi.slice(0, 3).join(' ') }}</span>
                    </div>
                    <div v-if="getHuangLiInfo(date.dateStr).ji.length > 0" class="week-yiji-item week-ji-item">
                      <span class="week-yiji-label week-ji-label">忌</span>
                      <span class="week-yiji-content">{{ getHuangLiInfo(date.dateStr).ji.slice(0, 3).join(' ') }}</span>
                    </div>
                  </div>
                  <!-- 建除十二值星（小标记） -->
                  <div v-if="getHuangLiInfo(date.dateStr).zhiXing" class="week-zhixing-mark">
                    <span class="week-zhixing-badge">{{ getHuangLiInfo(date.dateStr).zhiXing }}</span>
                  </div>
                </div>

                <div v-if="getOrdersForDate(date.dateStr).length > 0" class="week-order-info">
                  <el-tag size="small" type="primary">
                    {{ getOrdersForDate(date.dateStr).length }}单
                  </el-tag>
                </div>
                <div v-if="isHoliday(date.dateStr)" class="week-holiday-mark">
                  <el-tag size="small" type="danger">休</el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 年视图 -->
        <div v-else-if="viewMode === 'year'" class="year-view" v-loading="loading">
          <div class="year-header">
            <div class="year-title">{{ currentDate.getFullYear() }}年</div>
          </div>
          <div class="year-grid">
            <div
              v-for="month in 12"
              :key="month"
              class="year-month-card"
              @click="selectMonth(month)"
            >
              <div class="year-month-header">
                <span class="year-month-title">{{ month }}月</span>
                <span class="year-month-stats">{{ getMonthOrderStats(month) }}</span>
              </div>
              <div class="year-month-preview">
                <div class="year-month-calendar">
                  <div
                    v-for="day in getMonthPreviewDays(month)"
                    :key="day.dateStr"
                    class="year-day-cell"
                    :class="{
                      'has-orders': day.hasOrders,
                      'is-holiday': day.isHoliday,
                      'is-weekend': day.isWeekend
                    }"
                  >
                    {{ day.day }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 日期详情对话框 -->
    <el-dialog
      v-model="showDateDetail"
      :title="getDialogTitle()"
      width="1000px"
      @close="selectedDate = ''"
    >
      <!-- 日期汇总信息 -->
      <div class="date-summary" v-if="selectedDateOrders.length > 0">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="订单总数" :value="selectedDateOrders.length" suffix="单" />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="总金额"
              :value="getTotalAmount(selectedDateOrders)"
              prefix="¥"
              :precision="2"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="商品总数"
              :value="getTotalItems(selectedDateOrders)"
              suffix="件"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic
              title="已付款订单"
              :value="getPaidOrdersCount(selectedDateOrders)"
              suffix="单"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 商品分类汇总 -->
      <div class="category-summary" v-if="selectedDateOrders.length > 0">
        <h4>商品分类汇总</h4>
        <el-row :gutter="10">
          <el-col
            v-for="summary in getDetailedProductSummary(selectedDate)"
            :key="summary.category"
            :span="6"
          >
            <el-card class="category-card">
              <div class="category-info">
                <div class="category-name">{{ summary.category }}</div>
                <div class="category-stats">
                  <span>{{ summary.quantity }}件</span>
                  <span>¥{{ summary.amount.toFixed(2) }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 订单详情表格 -->
      <div class="orders-table">
        <h4>订单详情</h4>
        <el-table :data="selectedDateOrders" style="width: 100%" max-height="400">
          <el-table-column prop="id" label="订单号" width="80" />
          <el-table-column label="客户" width="120">
            <template #default="scope">
              {{ scope.row.customer?.name || scope.row.customers?.name || '未知客户' }}
            </template>
          </el-table-column>
          <el-table-column label="商品" min-width="200">
            <template #default="scope">
              <div class="product-list">
                <div
                  v-for="item in scope.row.order_items"
                  :key="item.id"
                  class="product-item"
                >
                  <span class="product-name">{{ item.product?.name || item.products?.name || '未知商品' }}</span>
                  <span class="product-quantity">x{{ item.quantity }}</span>
                  <span class="product-price">¥{{ (item.price_at_order * item.quantity).toFixed(2) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="total_price" label="总金额" width="100">
            <template #default="scope">
              ¥{{ parseFloat(scope.row.total_price || 0).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="付款状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.payment_status === 'paid' ? 'success' : 'danger'" size="small">
                {{ scope.row.payment_status === 'paid' ? '已付款' : '未付款' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="订单状态" width="100">
            <template #default="scope">
              <el-tag :type="getOrderStatusType(scope.row.order_status)" size="small">
                {{ getOrderStatusText(scope.row.order_status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="送货时间" width="120">
            <template #default="scope">
              {{ new Date(scope.row.delivery_datetime).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { ArrowLeft, ArrowRight, Refresh, Plus, Calendar, Check, Close } from '@element-plus/icons-vue'
import { useOrdersStore } from '../stores/orders'
import { solarToLunar, formatLunarDate, getSimpleHuangLiInfo, isHuangDaoJiRi } from '../lib/lunar-utils'
import { holidayAPI } from '../lib/api'

const ordersStore = useOrdersStore()

const viewMode = ref('month')
const currentDate = ref(new Date())
const showDateDetail = ref(false)
const selectedDate = ref('')
const holidays = ref([])
const loading = ref(false)
const lunarCache = ref(new Map())
const holidayCache = ref(new Map())
const orderCache = ref(new Map())
const showShortcuts = ref(true)
const showLunarInfo = ref(true) // 默认开启黄历显示


// 周视图相关数据
const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

// 计算属性
const selectedDateOrders = computed(() => {
  if (!selectedDate.value) return []
  return getOrdersForDate(selectedDate.value)
})

// 按日期分组的订单
const ordersByDate = computed(() => {
  const grouped = {}
  ordersStore.orders.forEach(order => {
    if (order.delivery_datetime) {
      const date = order.delivery_datetime.split('T')[0] // 获取日期部分
      if (!grouped[date]) grouped[date] = []
      grouped[date].push(order)
    }
  })
  return grouped
})

// 获取指定日期的订单（带缓存优化）
const getOrdersForDate = (date) => {
  // 检查缓存
  if (orderCache.value.has(date)) {
    return orderCache.value.get(date)
  }

  const orders = ordersByDate.value[date] || []

  // 缓存结果
  orderCache.value.set(date, orders)

  return orders
}

// 获取指定日期的商品汇总
const getProductSummaryForDate = (date) => {
  const orders = getOrdersForDate(date)
  const categoryMap = new Map()

  orders.forEach(order => {
    if (order.order_items && order.order_items.length > 0) {
      order.order_items.forEach(item => {
        const category = item.product?.category || item.products?.category || '其他'
        const quantity = parseInt(item.quantity) || 0

        if (categoryMap.has(category)) {
          categoryMap.set(category, categoryMap.get(category) + quantity)
        } else {
          categoryMap.set(category, quantity)
        }
      })
    }
  })

  // 转换为数组并按数量排序，只显示前3个分类
  const summaries = Array.from(categoryMap.entries())
    .map(([category, quantity]) => ({ category, quantity }))
    .sort((a, b) => b.quantity - a.quantity)
    .slice(0, 3)

  return summaries
}

// 获取农历日期（带缓存）
const getLunarDate = (date) => {
  try {
    // 检查缓存
    if (lunarCache.value.has(date)) {
      return lunarCache.value.get(date)
    }

    const lunarInfo = solarToLunar(date)
    const formatted = formatLunarDate(lunarInfo)

    // 缓存结果
    lunarCache.value.set(date, formatted)

    return formatted
  } catch (error) {
    console.error('获取农历日期失败:', error)
    return ''
  }
}

// 获取黄历信息（带缓存）
const getHuangLiInfo = (date) => {
  try {
    const cacheKey = `huangli_${date}`
    if (lunarCache.value.has(cacheKey)) {
      return lunarCache.value.get(cacheKey)
    }

    const huangLiInfo = getSimpleHuangLiInfo(date)

    // 缓存结果
    lunarCache.value.set(cacheKey, huangLiInfo)

    return huangLiInfo
  } catch (error) {
    console.error('获取黄历信息失败:', error)
    return {
      yi: [],
      ji: [],
      yiText: '',
      jiText: '',
      zhiXing: '',
      jieQi: '',
      festivals: []
    }
  }
}

// 判断是否为黄道吉日
const isGoodDay = (date) => {
  try {
    return isHuangDaoJiRi(date)
  } catch (error) {
    console.error('判断黄道吉日失败:', error)
    return false
  }
}

// 获取周的开始日期（周日）
const getWeekStart = (date) => {
  const current = new Date(date)
  const currentDay = current.getDay() // 0是周日
  const startDate = new Date(current)
  startDate.setDate(current.getDate() - currentDay)
  startDate.setHours(0, 0, 0, 0)
  return startDate
}

// 判断日期是否在当前周
const isInCurrentWeek = (dateStr) => {
  const date = new Date(dateStr)
  const currentWeekStart = getWeekStart(currentDate.value)
  const currentWeekEnd = new Date(currentWeekStart)
  currentWeekEnd.setDate(currentWeekStart.getDate() + 6)
  currentWeekEnd.setHours(23, 59, 59, 999)

  return date >= currentWeekStart && date <= currentWeekEnd
}

// 检查是否为节假日
const isHoliday = (date) => {
  return holidays.value.some(holiday => holiday.date === date && holiday.isHoliday)
}

// 检查是否为周末
const isWeekend = (date) => {
  const day = new Date(date).getDay()
  return day === 0 || day === 6 // 0是周日，6是周六
}

// 获取状态类型
const getOrderStatusType = (status) => {
  const statusMap = {
    'pending': 'danger',
    'production': 'info',
    'delivery': 'warning',
    'completed': 'success',
    'cancelled': ''
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

// 点击日期单元格
const handleDateClick = (date) => {
  const orders = getOrdersForDate(date.day)
  if (orders.length > 0) {
    selectedDate.value = date.day
    showDateDetail.value = true
  }
}

// 双击日期单元格 - 切换到周视图
const handleDateDoubleClick = (date) => {
  // 设置当前日期为双击的日期
  currentDate.value = new Date(date.day)
  // 切换到周视图
  viewMode.value = 'week'
}

// 周视图：点击日期单元格
const handleWeekDateClick = (date) => {
  const orders = getOrdersForDate(date.dateStr)
  if (orders.length > 0) {
    selectedDate.value = date.dateStr
    showDateDetail.value = true
  }
}

// 获取周视图标题
const getWeekTitle = () => {
  const weekDates = getWeekDates()
  if (weekDates.length === 0) return ''

  const firstDate = weekDates[0].dateStr
  const lastDate = weekDates[weekDates.length - 1].dateStr

  return `${firstDate} 至 ${lastDate}`
}

// 获取当前周的日期数组
const getWeekDates = () => {
  const current = new Date(currentDate.value)
  const currentDay = current.getDay() // 0是周日
  const dates = []

  // 计算本周的开始日期（周日）
  const startDate = new Date(current)
  startDate.setDate(current.getDate() - currentDay)

  // 生成一周的日期
  for (let i = 0; i < 7; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)

    dates.push({
      dateStr: date.toISOString().split('T')[0],
      day: date.getDate(),
      date: date
    })
  }

  return dates
}

// 检查是否为今天
const isToday = (dateStr) => {
  const today = new Date().toISOString().split('T')[0]
  return dateStr === today
}

// 年视图：选择月份
const selectMonth = (month) => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(month - 1)
  currentDate.value = newDate
  viewMode.value = 'month'
}

// 获取月份订单统计
const getMonthOrderStats = (month) => {
  const year = currentDate.value.getFullYear()
  const monthOrders = ordersStore.orders.filter(order => {
    if (!order.delivery_datetime) return false
    const orderDate = new Date(order.delivery_datetime)
    return orderDate.getFullYear() === year && orderDate.getMonth() === month - 1
  })

  if (monthOrders.length === 0) return ''
  return `${monthOrders.length}单`
}

// 获取月份预览天数
const getMonthPreviewDays = (month) => {
  const year = currentDate.value.getFullYear()
  const daysInMonth = new Date(year, month, 0).getDate()
  const days = []

  for (let day = 1; day <= Math.min(daysInMonth, 31); day++) {
    const dateStr = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
    const orders = getOrdersForDate(dateStr)

    days.push({
      day,
      dateStr,
      hasOrders: orders.length > 0,
      isHoliday: isHoliday(dateStr),
      isWeekend: isWeekend(dateStr)
    })
  }

  return days
}

// 获取节假日数据（带缓存）
const fetchHolidays = async (year) => {
  try {
    // 检查缓存
    const cacheKey = `holidays_${year}`
    if (holidayCache.value.has(cacheKey)) {
      holidays.value = holidayCache.value.get(cacheKey)
      return
    }

    loading.value = true

    // 使用后端API获取节假日数据
    const result = await holidayAPI.getHolidays({ year })

    let holidayData = []
    if (result.success && Array.isArray(result.data)) {
      holidayData = result.data.map(holiday => ({
        date: holiday.date.split('T')[0], // 只取日期部分
        name: holiday.name,
        isHoliday: holiday.type === 'holiday'
      }))
    } else {
      // 如果后端没有数据，尝试同步
      const syncResult = await holidayAPI.syncHolidays(year)
      if (syncResult.success) {
        // 重新获取数据
        const retryResult = await holidayAPI.getHolidays({ year })
        if (retryResult.success && Array.isArray(retryResult.data)) {
          holidayData = retryResult.data.map(holiday => ({
            date: holiday.date.split('T')[0],
            name: holiday.name,
            isHoliday: holiday.type === 'holiday'
          }))
        }
      }
    }

    holidays.value = holidayData
    // 缓存结果（缓存1小时）
    holidayCache.value.set(cacheKey, holidayData)
    setTimeout(() => {
      holidayCache.value.delete(cacheKey)
    }, 60 * 60 * 1000)
  } catch (error) {
    console.error('获取节假日数据失败:', error)
    // 使用默认节假日数据
    holidays.value = [
      { date: '2024-01-01', name: '元旦', isHoliday: true },
      { date: '2024-02-10', name: '春节', isHoliday: true },
      { date: '2024-02-11', name: '春节', isHoliday: true },
      { date: '2024-02-12', name: '春节', isHoliday: true },
      { date: '2024-04-04', name: '清明节', isHoliday: true },
      { date: '2024-05-01', name: '劳动节', isHoliday: true },
      { date: '2024-06-10', name: '端午节', isHoliday: true },
      { date: '2024-09-17', name: '中秋节', isHoliday: true },
      { date: '2024-10-01', name: '国庆节', isHoliday: true },
      { date: '2024-10-02', name: '国庆节', isHoliday: true },
      { date: '2024-10-03', name: '国庆节', isHoliday: true }
    ]
  } finally {
    loading.value = false
  }
}

// 格式化商品列表
const formatProducts = (orderItems) => {
  if (!orderItems || orderItems.length === 0) return '无商品'

  return orderItems.map(item => {
    const productName = item.product?.name || item.products?.name || '未知商品'
    return `${productName} x${item.quantity}`
  }).join(', ')
}

// 获取弹窗标题
const getDialogTitle = () => {
  if (!selectedDate.value) return '订单详情'

  const date = new Date(selectedDate.value)
  const lunarInfo = solarToLunar(selectedDate.value)
  const holiday = holidays.value.find(h => h.date === selectedDate.value)

  let title = `${selectedDate.value} (${date.toLocaleDateString('zh-CN', { weekday: 'long' })})`

  if (lunarInfo.lunarDateStr) {
    title += ` 农历${lunarInfo.lunarDateStr}`
  }

  if (holiday) {
    title += ` ${holiday.name}`
  }

  return title
}

// 计算总金额
const getTotalAmount = (orders) => {
  return orders.reduce((total, order) => total + parseFloat(order.total_price || 0), 0)
}

// 计算总商品数
const getTotalItems = (orders) => {
  return orders.reduce((total, order) => {
    if (order.order_items) {
      return total + order.order_items.reduce((itemTotal, item) => itemTotal + parseInt(item.quantity || 0), 0)
    }
    return total
  }, 0)
}

// 计算已付款订单数
const getPaidOrdersCount = (orders) => {
  return orders.filter(order => order.payment_status === 'paid').length
}

// 获取详细的商品汇总
const getDetailedProductSummary = (date) => {
  const orders = getOrdersForDate(date)
  const categoryMap = new Map()

  orders.forEach(order => {
    if (order.order_items && order.order_items.length > 0) {
      order.order_items.forEach(item => {
        const category = item.product?.category || item.products?.category || '其他'
        const quantity = parseInt(item.quantity) || 0
        const amount = parseFloat(item.price_at_order || 0) * quantity

        if (categoryMap.has(category)) {
          const existing = categoryMap.get(category)
          categoryMap.set(category, {
            quantity: existing.quantity + quantity,
            amount: existing.amount + amount
          })
        } else {
          categoryMap.set(category, { quantity, amount })
        }
      })
    }
  })

  return Array.from(categoryMap.entries())
    .map(([category, data]) => ({ category, ...data }))
    .sort((a, b) => b.amount - a.amount)
}

// 清理缓存
const clearCache = () => {
  orderCache.value.clear()
  lunarCache.value.clear()
}

// 获取今日信息
const getTodayInfo = () => {
  const today = new Date()
  const todayStr = today.toISOString().split('T')[0]

  return {
    solar: today.toLocaleDateString('zh-CN'),
    lunar: getLunarDate(todayStr),
    weekday: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][today.getDay()],
    solarTerm: getSolarTerm(todayStr)
  }
}

// 获取节气信息（简化版本）
const getSolarTerm = (date) => {
  const month = new Date(date).getMonth() + 1
  const day = new Date(date).getDate()

  // 简化的节气判断（实际应该更精确）
  const solarTerms = {
    '6-5': '芒种',
    '6-21': '夏至',
    '7-7': '小暑',
    '7-22': '大暑',
    '8-7': '立秋',
    '8-23': '处暑',
    '9-7': '白露',
    '9-23': '秋分',
    '10-8': '寒露',
    '10-23': '霜降'
  }

  return solarTerms[`${month}-${day}`] || null
}

// 获取皇历建议（演示数据）
const getLunarAdvice = () => {
  const today = new Date()
  const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / 86400000)

  // 根据日期生成不同的建议（演示用）
  const goodAdvices = [
    ['开业', '搬家', '结婚', '出行'],
    ['祭祀', '祈福', '纳财', '安床'],
    ['动土', '装修', '签约', '交易'],
    ['会友', '宴请', '学习', '读书'],
    ['种植', '养殖', '投资', '理财']
  ]

  const badAdvices = [
    ['动土', '破土', '开仓', '出货'],
    ['结婚', '搬家', '开业', '签约'],
    ['出行', '远行', '投资', '交易'],
    ['祭祀', '安葬', '修造', '装修'],
    ['宴请', '聚会', '饮酒', '娱乐']
  ]

  const goodIndex = dayOfYear % goodAdvices.length
  const badIndex = (dayOfYear + 2) % badAdvices.length

  return {
    good: goodAdvices[goodIndex],
    bad: badAdvices[badIndex]
  }
}

// 监听订单数据变化，清理缓存
watch(() => ordersStore.orders, () => {
  clearCache()
}, { deep: true })

// 监听当前日期变化，获取对应年份的节假日
watch(currentDate, (newDate) => {
  const year = newDate.getFullYear()
  fetchHolidays(year)
  // 切换年份时清理农历缓存
  lunarCache.value.clear()
}, { immediate: true })

// 格式化当前日期显示
const formatCurrentDate = () => {
  const date = currentDate.value
  const year = date.getFullYear()
  const month = date.getMonth() + 1

  if (viewMode.value === 'year') {
    return `${year}年`
  } else {
    return `${year}年${month}月`
  }
}

// 格式化日历标题（用于自定义日历头部）
const formatCalendarTitle = (date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  return `${year}年${month}月`
}

// 日历导航（用于自定义日历头部）
const navigateCalendar = (direction) => {
  const date = new Date(currentDate.value)
  if (direction === 'next') {
    date.setMonth(date.getMonth() + 1)
  } else {
    date.setMonth(date.getMonth() - 1)
  }
  currentDate.value = date
}

// 导航日期
const navigateDate = (direction) => {
  const date = new Date(currentDate.value)

  if (viewMode.value === 'year') {
    date.setFullYear(date.getFullYear() + (direction === 'next' ? 1 : -1))
  } else if (viewMode.value === 'month') {
    date.setMonth(date.getMonth() + (direction === 'next' ? 1 : -1))
  } else if (viewMode.value === 'week') {
    date.setDate(date.getDate() + (direction === 'next' ? 7 : -7))
  }

  currentDate.value = date
}

// 回到今天
const goToToday = () => {
  currentDate.value = new Date()
}

// 刷新数据
const refreshData = async () => {
  clearCache()
  await ordersStore.fetchOrders()
  const year = currentDate.value.getFullYear()
  await fetchHolidays(year)
}



// 键盘快捷键处理
const handleKeydown = (event) => {
  if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
    return
  }

  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault()
      navigateDate('prev')
      break
    case 'ArrowRight':
      event.preventDefault()
      navigateDate('next')
      break
    case 't':
    case 'T':
      event.preventDefault()
      goToToday()
      break
    case 'r':
    case 'R':
      event.preventDefault()
      refreshData()
      break
    case '1':
      event.preventDefault()
      viewMode.value = 'month'
      break
    case '2':
      event.preventDefault()
      viewMode.value = 'week'
      break
    case '3':
      event.preventDefault()
      viewMode.value = 'year'
      break
  }
}

// 组件挂载时获取数据
onMounted(async () => {
  await ordersStore.fetchOrders()
  const currentYear = new Date().getFullYear()
  await fetchHolidays(currentYear)

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.calendar-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.date-navigation {
  display: flex;
  align-items: center;
  gap: 12px;
}

.current-date-display {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  min-width: 120px;
  text-align: center;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.calendar-container {
  min-height: 600px;
  transition: all 0.3s ease;
}

.calendar-container.loading {
  opacity: 0.7;
  pointer-events: none;
}

.calendar-cell {
  height: 100%;
  min-height: 80px;
  padding: 4px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease, border-color 0.2s ease;
  border-radius: 4px;
  overflow: hidden;
}

.calendar-cell:hover {
  background-color: #f5f7fa;
}

.calendar-cell.has-orders {
  background-color: #ecf5ff;
  border: 2px solid #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.calendar-cell.is-holiday {
  background-color: #fef0f0;
  border: 1px solid #f56c6c;
}

.calendar-cell.is-holiday .solar-date {
  color: #f56c6c;
}

.calendar-cell.is-weekend {
  background-color: #fafafa;
}

.calendar-cell.is-weekend .solar-date {
  color: #909399;
}

.calendar-cell.is-holiday.has-orders {
  background: linear-gradient(135deg, #fef0f0 0%, #ecf5ff 100%);
  border: 2px solid #f56c6c;
}

.calendar-cell.is-good-day {
  background: linear-gradient(135deg, #fff8e1 0%, #f3e5ab 100%);
  border: 1px solid #ffc107;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.calendar-cell.is-good-day .solar-date {
  color: #e65100;
  font-weight: bold;
}

/* 非当前月份日期弱化显示 */
.calendar-cell.is-other-month {
  opacity: 0.4;
  background: #fafafa !important;
}

.calendar-cell.is-other-month .solar-date {
  color: #c0c4cc;
}

.calendar-cell.is-other-month .lunar-date {
  color: #e4e7ed;
}

.calendar-cell.is-other-month .huangli-info {
  opacity: 0.6;
}

.date-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 4px;
}

.solar-date {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  line-height: 1.2;
}

.lunar-date {
  font-size: 11px;
  color: #909399;
  line-height: 1.2;
  margin-top: 1px;
}

/* 黄历信息样式 - 月视图 */
.huangli-info {
  margin-top: 4px;
  font-size: 9px;
  line-height: 1.2;
  max-height: 50px;
  overflow: hidden;
}

/* 特殊信息（节气、节日）样式 */
.special-info {
  margin-bottom: 2px;
  text-align: center;
}

.special-tag {
  display: inline-block;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 8px;
  font-weight: 500;
  line-height: 1.2;
}

.jieqi-tag {
  background: #fff2e8;
  color: #e6a23c;
  border: 1px solid #f5dab1;
}

.festival-tag {
  background: #f0f9ff;
  color: #1890ff;
  border: 1px solid #b3d8ff;
}

/* 宜忌信息容器 */
.yiji-container {
  margin-top: 2px;
}

.yiji-item {
  display: flex;
  align-items: center;
  margin-bottom: 1px;
  font-size: 8px;
  line-height: 1.1;
}

.yiji-label {
  display: inline-block;
  width: 12px;
  text-align: center;
  font-weight: bold;
  border-radius: 2px;
  padding: 0 1px;
  margin-right: 2px;
  font-size: 7px;
}

.yi-label {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.ji-label {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.yiji-content {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}

/* 建除十二值星标记 */
.zhixing-mark {
  position: absolute;
  bottom: 2px;
  left: 2px;
  z-index: 1;
}

.zhixing-badge {
  display: inline-block;
  width: 12px;
  height: 12px;
  line-height: 12px;
  text-align: center;
  font-size: 7px;
  font-weight: bold;
  color: #e6a23c;
  background: rgba(230, 162, 60, 0.15);
  border: 1px solid rgba(230, 162, 60, 0.4);
  border-radius: 50%;
}

.order-info {
  position: absolute;
  top: 4px;
  right: 4px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  max-width: 50%;
  z-index: 2;
}

.holiday-mark {
  position: absolute;
  bottom: 4px;
  right: 4px;
  z-index: 3;
}



.product-summary {
  margin-top: 4px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.category-tag {
  font-size: 10px;
  padding: 1px 4px;
  margin: 0;
}

/* 弹窗样式 */
.date-summary {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.category-summary {
  margin-bottom: 20px;
}

.category-summary h4 {
  margin-bottom: 12px;
  color: #303133;
}

.category-card {
  margin-bottom: 8px;
}

.category-info {
  text-align: center;
}

.category-name {
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.category-stats {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #606266;
}

.orders-table h4 {
  margin-bottom: 12px;
  color: #303133;
}

.product-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
}

.product-name {
  flex: 1;
  color: #303133;
}

.product-quantity {
  color: #909399;
  margin: 0 8px;
}

.product-price {
  color: #409eff;
  font-weight: bold;
}

/* 快捷键提示样式 */
.keyboard-shortcuts {
  margin-bottom: 16px;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-top: 8px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

kbd {
  display: inline-block;
  padding: 2px 6px;
  font-size: 12px;
  line-height: 1.4;
  color: #444;
  background-color: #fafbfc;
  border: 1px solid #d1d5da;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 #d1d5da;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

/* 周视图样式 */
.week-view {
  min-height: 400px;
}

.week-header {
  text-align: center;
  margin-bottom: 20px;
}

.week-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.week-grid {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
}

.week-days-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background-color: #f5f7fa;
}

.week-day-header {
  padding: 12px;
  text-align: center;
  font-weight: bold;
  color: #606266;
  border-right: 1px solid #dcdfe6;
}

.week-day-header:last-child {
  border-right: none;
}

.week-days-content {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  min-height: 300px;
}

.week-day-cell {
  padding: 16px;
  border-right: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.week-day-cell:last-child {
  border-right: none;
}

.week-day-cell:hover {
  background-color: #f5f7fa;
}

.week-day-cell.has-orders {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.week-day-cell.is-holiday {
  background-color: #fef0f0;
  border-color: #f56c6c;
}

.week-day-cell.is-weekend {
  background-color: #fafafa;
}

.week-day-cell.is-today {
  background-color: #e6f7ff;
  border: 2px solid #1890ff;
}

.week-day-cell.is-good-day {
  background: linear-gradient(135deg, #fff8e1 0%, #f3e5ab 100%);
  border: 1px solid #ffc107;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

/* 非当前周日期弱化显示 */
.week-day-cell.is-other-week {
  opacity: 0.4;
  background: #fafafa !important;
}

.week-day-cell.is-other-week .week-solar-date {
  color: #c0c4cc;
}

.week-day-cell.is-other-week .week-lunar-date {
  color: #e4e7ed;
}

.week-day-cell.is-other-week .week-huangli-info {
  opacity: 0.6;
}

.week-date-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 8px;
}

.week-solar-date {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.week-lunar-date {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.week-order-info {
  text-align: center;
  margin-bottom: 8px;
}

.week-holiday-mark {
  position: absolute;
  top: 8px;
  right: 8px;
}

/* 周视图黄历信息样式 */
.week-huangli-info {
  margin-top: 8px;
  font-size: 11px;
  line-height: 1.3;
  max-height: 80px;
  overflow: hidden;
}

/* 周视图特殊信息（节气、节日）样式 */
.week-special-info {
  margin-bottom: 4px;
  text-align: center;
}

.week-special-tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  line-height: 1.2;
}

.week-jieqi-tag {
  background: #fff2e8;
  color: #e6a23c;
  border: 1px solid #f5dab1;
}

.week-festival-tag {
  background: #f0f9ff;
  color: #1890ff;
  border: 1px solid #b3d8ff;
}

/* 周视图宜忌信息容器 */
.week-yiji-container {
  margin-top: 4px;
}

.week-yiji-item {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  font-size: 10px;
  line-height: 1.2;
}

.week-yiji-label {
  display: inline-block;
  width: 16px;
  text-align: center;
  font-weight: bold;
  border-radius: 3px;
  padding: 1px 2px;
  margin-right: 4px;
  font-size: 9px;
}

.week-yi-label {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.week-ji-label {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.week-yiji-content {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}

/* 周视图建除十二值星标记 */
.week-zhixing-mark {
  position: absolute;
  bottom: 8px;
  left: 8px;
  z-index: 1;
}

.week-zhixing-badge {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  font-size: 9px;
  font-weight: bold;
  color: #e6a23c;
  background: rgba(230, 162, 60, 0.15);
  border: 1px solid rgba(230, 162, 60, 0.4);
  border-radius: 50%;
}

/* 年视图样式 */
.year-view {
  min-height: 600px;
}

.year-header {
  text-align: center;
  margin-bottom: 30px;
}

.year-title {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.year-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.year-month-card {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  background-color: #fff;
}

.year-month-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.year-month-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.year-month-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.year-month-stats {
  font-size: 12px;
  color: #409eff;
  font-weight: bold;
}

.year-month-preview {
  height: 120px;
}

.year-month-calendar {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  height: 100%;
}

.year-day-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #909399;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.year-day-cell.has-orders {
  background-color: #409eff;
  color: #fff;
}

.year-day-cell.is-holiday {
  background-color: #f56c6c;
  color: #fff;
}

.year-day-cell.is-weekend {
  background-color: #e4e7ed;
}

/* 自定义日历头部样式 */
.custom-calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  margin: -12px -12px 12px -12px;
}

.calendar-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.calendar-nav {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 皇历信息面板样式 */
.lunar-info-panel {
  margin-bottom: 16px;
}

.lunar-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.lunar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #303133;
}

.lunar-content {
  padding: 0;
}

.lunar-item {
  text-align: center;
  padding: 12px 0;
}

.lunar-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.lunar-value {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.lunar-section {
  padding: 12px 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 14px;
}

.section-content {
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.5;
  min-height: 40px;
  display: flex;
  align-items: center;
}

.section-content.good {
  background-color: #f0f9ff;
  color: #67C23A;
  border: 1px solid #e1f5fe;
}

.section-content.bad {
  background-color: #fef2f2;
  color: #F56C6C;
  border: 1px solid #fecaca;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .header-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .date-navigation {
    justify-content: center;
  }

  .view-controls {
    justify-content: center;
  }

  .shortcuts-grid {
    grid-template-columns: 1fr;
  }

  .year-grid {
    grid-template-columns: 1fr;
  }

  .week-days-content {
    min-height: 200px;
  }

  .week-day-cell {
    padding: 8px;
  }

  .week-solar-date {
    font-size: 18px;
  }
}
</style>
