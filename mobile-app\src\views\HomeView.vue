<template>
  <div class="page">
    <div class="page-content">
      <!-- 欢迎卡片 -->
      <div class="card">
        <div class="card-body">
          <div class="welcome-section">
            <h2>欢迎使用小商店订单系统</h2>
            <p class="welcome-text">管理您的订单、商品和客户信息</p>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="card">
        <div class="card-header">
          <span>快速操作</span>
        </div>
        <div class="card-body">
          <div class="quick-actions">
            <div 
              class="action-item touch-feedback"
              @click="navigateTo('/orders')"
            >
              <el-icon :size="24" class="action-icon">
                <Document />
              </el-icon>
              <span class="action-label">新建订单</span>
            </div>
            <div 
              class="action-item touch-feedback"
              @click="navigateTo('/calendar')"
            >
              <el-icon :size="24" class="action-icon">
                <Calendar />
              </el-icon>
              <span class="action-label">查看日历</span>
            </div>
            <div 
              class="action-item touch-feedback"
              @click="navigateTo('/products')"
            >
              <el-icon :size="24" class="action-icon">
                <Goods />
              </el-icon>
              <span class="action-label">商品管理</span>
            </div>
            <div 
              class="action-item touch-feedback"
              @click="navigateTo('/customers')"
            >
              <el-icon :size="24" class="action-icon">
                <User />
              </el-icon>
              <span class="action-label">客户管理</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 今日概览 -->
      <div class="card">
        <div class="card-header">
          <span>今日概览</span>
          <el-button 
            type="text" 
            size="small"
            @click="refreshData"
            :loading="loading"
          >
            刷新
          </el-button>
        </div>
        <div class="card-body">
          <div v-if="loading" class="loading-container">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span style="margin-left: 8px;">加载中...</span>
          </div>
          <div v-else class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ todayStats.orderCount }}</div>
              <div class="stat-label">今日订单</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">¥{{ todayStats.totalAmount }}</div>
              <div class="stat-label">今日金额</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ todayStats.productCount }}</div>
              <div class="stat-label">商品种类</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ todayStats.customerCount }}</div>
              <div class="stat-label">客户数量</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近订单 -->
      <div class="card">
        <div class="card-header">
          <span>最近订单</span>
          <el-button 
            type="text" 
            size="small"
            @click="navigateTo('/orders')"
          >
            查看全部
          </el-button>
        </div>
        <div class="card-body">
          <div v-if="recentOrders.length === 0" class="empty-state">
            <el-icon><Document /></el-icon>
            <div class="empty-state-text">暂无订单</div>
          </div>
          <div v-else class="order-list">
            <div 
              v-for="order in recentOrders"
              :key="order.id"
              class="order-item touch-feedback"
              @click="navigateTo(`/orders/${order.id}`)"
            >
              <div class="order-info">
                <div class="order-title">{{ order.customer_name }}</div>
                <div class="order-meta">
                  {{ formatDate(order.delivery_date) }} · ¥{{ order.total_amount }}
                </div>
              </div>
              <el-tag 
                :type="getStatusType(order.status)"
                size="small"
              >
                {{ getStatusText(order.status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Document, 
  Calendar, 
  Goods, 
  User, 
  Loading 
} from '@element-plus/icons-vue'
import { orderAPI } from '@/utils/api'

const router = useRouter()
const loading = ref(false)
const todayStats = ref({
  orderCount: 0,
  totalAmount: '0.00',
  productCount: 0,
  customerCount: 0
})
const recentOrders = ref([])

const navigateTo = (path) => {
  router.push(path)
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'confirmed': 'primary',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待确认',
    'confirmed': '已确认',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

const loadTodayStats = async () => {
  const today = new Date().toISOString().split('T')[0]
  const result = await orderAPI.getDailySummary(today)
  
  if (result.success) {
    todayStats.value = {
      orderCount: result.data.order_count || 0,
      totalAmount: (result.data.total_amount || 0).toFixed(2),
      productCount: result.data.product_count || 0,
      customerCount: result.data.customer_count || 0
    }
  }
}

const loadRecentOrders = async () => {
  const result = await orderAPI.getOrders({ limit: 5, sort: 'created_at', order: 'desc' })
  
  if (result.success) {
    recentOrders.value = result.data || []
  }
}

const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadTodayStats(),
      loadRecentOrders()
    ])
  } catch (error) {
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.welcome-section {
  text-align: center;
  padding: 20px 0;
}

.welcome-section h2 {
  color: var(--text-primary);
  margin-bottom: 8px;
  font-size: 20px;
  font-weight: 600;
}

.welcome-text {
  color: var(--text-secondary);
  font-size: 14px;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  cursor: pointer;
}

.action-icon {
  color: var(--primary-color);
  margin-bottom: 8px;
}

.action-label {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  border-radius: var(--border-radius);
  background: var(--bg-page);
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  cursor: pointer;
}

.order-info {
  flex: 1;
  min-width: 0;
}

.order-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.order-meta {
  font-size: 12px;
  color: var(--text-secondary);
}
</style>
