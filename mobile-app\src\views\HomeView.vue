<template>
  <div class="page">
    <div class="page-content">
      <!-- 欢迎卡片 -->
      <div class="card">
        <div class="card-body">
          <div class="welcome-section">
            <h2>欢迎使用小商店订单系统</h2>
            <p class="welcome-text">管理您的订单、商品和客户信息</p>
          </div>
        </div>
      </div>

      <!-- 核心统计 -->
      <div class="card">
        <div class="card-header">
          <span>核心数据</span>
          <el-button
            type="text"
            size="small"
            @click="refreshData"
            :loading="loading"
          >
            刷新
          </el-button>
        </div>
        <div class="card-body">
          <div v-if="loading" class="loading-container">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span style="margin-left: 8px;">加载中...</span>
          </div>
          <div v-else class="core-stats">
            <div class="stat-card sales touch-feedback" @click="navigateTo('/orders')">
              <div class="stat-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">¥{{ dashboardStats.totalSales }}</div>
                <div class="stat-label">总销售额</div>
              </div>
            </div>
            <div class="stat-card orders touch-feedback" @click="navigateTo('/orders')">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ dashboardStats.totalOrders }}</div>
                <div class="stat-label">订单总数</div>
              </div>
            </div>
            <div class="stat-card pending touch-feedback" @click="goToPendingOrders">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ dashboardStats.pendingOrders }}</div>
                <div class="stat-label">待处理订单</div>
              </div>
            </div>
            <div class="stat-card customers touch-feedback" @click="navigateTo('/customers')">
              <div class="stat-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ dashboardStats.totalCustomers }}</div>
                <div class="stat-label">客户总数</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 今日概览 -->
      <div class="card">
        <div class="card-header">
          <span>今日概览</span>
          <span class="date-info">{{ formatToday() }}</span>
        </div>
        <div class="card-body">
          <div class="today-stats">
            <div class="today-stat-item touch-feedback" @click="goToTodayOrders">
              <div class="today-stat-value">{{ todayStats.orderCount }}</div>
              <div class="today-stat-label">今日订单</div>
            </div>
            <div class="today-stat-item touch-feedback" @click="goToTodayOrders">
              <div class="today-stat-value">¥{{ todayStats.totalAmount }}</div>
              <div class="today-stat-label">今日金额</div>
            </div>
            <div class="today-stat-item touch-feedback" @click="goToPaidOrders">
              <div class="today-stat-value">{{ todayStats.paidOrders }}</div>
              <div class="today-stat-label">已付款</div>
            </div>
            <div class="today-stat-item touch-feedback" @click="goToCompletedOrders">
              <div class="today-stat-value">{{ todayStats.completedOrders }}</div>
              <div class="today-stat-label">已完成</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="card">
        <div class="card-header">
          <span>快速操作</span>
        </div>
        <div class="card-body">
          <div class="quick-actions">
            <div
              class="action-item touch-feedback"
              @click="navigateTo('/orders/create')"
            >
              <el-icon :size="24" class="action-icon">
                <Document />
              </el-icon>
              <span class="action-label">新建订单</span>
            </div>
            <div
              class="action-item touch-feedback"
              @click="navigateTo('/calendar')"
            >
              <el-icon :size="24" class="action-icon">
                <Calendar />
              </el-icon>
              <span class="action-label">查看日历</span>
            </div>
            <div
              class="action-item touch-feedback"
              @click="navigateTo('/products')"
            >
              <el-icon :size="24" class="action-icon">
                <Goods />
              </el-icon>
              <span class="action-label">商品管理</span>
            </div>
            <div
              class="action-item touch-feedback"
              @click="navigateTo('/customers')"
            >
              <el-icon :size="24" class="action-icon">
                <User />
              </el-icon>
              <span class="action-label">客户管理</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近订单 -->
      <div class="card">
        <div class="card-header">
          <span>最近订单</span>
          <el-button 
            type="text" 
            size="small"
            @click="navigateTo('/orders')"
          >
            查看全部
          </el-button>
        </div>
        <div class="card-body">
          <div v-if="recentOrders.length === 0" class="empty-state">
            <el-icon><Document /></el-icon>
            <div class="empty-state-text">暂无订单</div>
          </div>
          <div v-else class="order-list">
            <div 
              v-for="order in recentOrders"
              :key="order.id"
              class="order-item touch-feedback"
              @click="navigateTo(`/orders/${order.id}`)"
            >
              <div class="order-info">
                <div class="order-title">{{ order.customer?.name || '未知客户' }}</div>
                <div class="order-meta">
                  {{ formatDate(order.delivery_datetime) }} · ¥{{ order.total_price }}
                </div>
              </div>
              <el-tag
                :type="getStatusType(order.order_status)"
                size="small"
              >
                {{ getStatusText(order.order_status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Document,
  Calendar,
  Goods,
  User,
  Loading,
  Money,
  Clock
} from '@element-plus/icons-vue'
import { orderAPI, productAPI, customerAPI } from '@/utils/api'

const router = useRouter()
const loading = ref(false)

// 仪表板统计数据
const dashboardStats = ref({
  totalSales: '0.00',
  totalOrders: 0,
  pendingOrders: 0,
  totalCustomers: 0
})

// 今日统计数据
const todayStats = ref({
  orderCount: 0,
  totalAmount: '0.00',
  paidOrders: 0,
  completedOrders: 0
})

const recentOrders = ref([])

const navigateTo = (path) => {
  router.push(path)
}

// 跳转到待处理订单
const goToPendingOrders = () => {
  router.push('/orders?status=pending')
}

// 跳转到今日订单
const goToTodayOrders = () => {
  const today = new Date().toISOString().split('T')[0]
  router.push(`/orders?date=${today}`)
  console.log('Navigating to today orders with date:', today)
}

// 跳转到已付款订单
const goToPaidOrders = () => {
  router.push('/orders?payment_status=paid')
}

// 跳转到已完成订单
const goToCompletedOrders = () => {
  router.push('/orders?status=completed')
}

const formatToday = () => {
  const today = new Date()
  return today.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'production': 'primary',
    'delivery': 'info',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '待处理',
    'production': '生产中',
    'delivery': '配送中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

// 加载仪表板统计数据
const loadDashboardStats = async () => {
  try {
    // 并行加载所有统计数据
    const [ordersResult, customersResult] = await Promise.all([
      orderAPI.getOrders({ limit: 1000 }), // 获取所有订单用于统计
      customerAPI.getCustomers()
    ])

    if (ordersResult.success) {
      const orders = ordersResult.data || []
      const totalSales = orders.reduce((sum, order) => sum + (parseFloat(order.total_price) || 0), 0)
      const pendingOrders = orders.filter(order => order.order_status === 'pending').length

      dashboardStats.value = {
        totalSales: totalSales.toFixed(2),
        totalOrders: orders.length,
        pendingOrders: pendingOrders,
        totalCustomers: customersResult.success ? (customersResult.data || []).length : 0
      }
    }
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
}

const loadTodayStats = async () => {
  const today = new Date().toISOString().split('T')[0]

  try {
    const result = await orderAPI.getOrders({
      delivery_datetime: today,
      limit: 1000
    })

    if (result.success) {
      const todayOrders = result.data || []
      const totalAmount = todayOrders.reduce((sum, order) => sum + (parseFloat(order.total_price) || 0), 0)
      const paidOrders = todayOrders.filter(order => order.payment_status === 'paid').length
      const completedOrders = todayOrders.filter(order => order.order_status === 'completed').length

      todayStats.value = {
        orderCount: todayOrders.length,
        totalAmount: totalAmount.toFixed(2),
        paidOrders: paidOrders,
        completedOrders: completedOrders
      }
    }
  } catch (error) {
    console.error('加载今日数据失败:', error)
  }
}

const loadRecentOrders = async () => {
  const result = await orderAPI.getOrders({ limit: 5, sort: 'created_at', order: 'desc' })

  if (result.success) {
    recentOrders.value = result.data || []
  }
}

const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadDashboardStats(),
      loadTodayStats(),
      loadRecentOrders()
    ])
  } catch (error) {
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.welcome-section {
  text-align: center;
  padding: 20px 0;
}

.welcome-section h2 {
  color: var(--text-primary);
  margin-bottom: 8px;
  font-size: 20px;
  font-weight: 600;
}

.welcome-text {
  color: var(--text-secondary);
  font-size: 14px;
}

/* 核心统计卡片 */
.core-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: var(--border-radius);
  background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-page) 100%);
  border: 1px solid var(--border-color);
}

.stat-card.sales {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card.orders {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stat-card.pending {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-card.customers {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  background: rgba(255, 255, 255, 0.2);
  font-size: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
}

/* 今日统计 */
.date-info {
  font-size: 12px;
  color: var(--text-secondary);
}

.today-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.today-stat-item {
  text-align: center;
  padding: 16px;
  border-radius: var(--border-radius);
  background: var(--bg-page);
  border: 1px solid var(--border-color);
}

.today-stat-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.today-stat-label {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 快速操作 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-item:hover {
  border-color: var(--primary-color);
  background: rgba(64, 158, 255, 0.05);
}

.action-icon {
  color: var(--primary-color);
  margin-bottom: 8px;
}

.action-label {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  cursor: pointer;
}

.order-info {
  flex: 1;
  min-width: 0;
}

.order-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.order-meta {
  font-size: 12px;
  color: var(--text-secondary);
}
</style>
