/* 移动端专用样式 */

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 移动端变量 */
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  --text-primary: #333;
  --text-regular: #666;
  --text-secondary: #999;
  
  --border-color: #e0e0e0;
  --bg-color: #ffffff;
  --bg-page: #f5f5f5;
  
  --header-height: 50px;
  --tabbar-height: 60px;
  
  --border-radius: 8px;
  --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 安全区域适配 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  :root {
    --safe-area-bottom: env(safe-area-inset-bottom);
  }
}

/* 通用布局类 */
.page {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: calc(var(--tabbar-height) + 16px);
}

.card {
  background: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 16px;
  overflow: hidden;
}

.card-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
  font-size: 16px;
}

.card-body {
  padding: 16px;
}

/* 按钮样式优化 */
.el-button {
  border-radius: var(--border-radius);
  font-weight: 500;
}

.el-button--small {
  padding: 8px 12px;
  font-size: 13px;
}

/* 表单样式优化 */
.el-input__wrapper {
  border-radius: var(--border-radius);
}

.el-select .el-input__wrapper {
  border-radius: var(--border-radius);
}

/* 对话框样式优化 */
.el-dialog {
  border-radius: var(--border-radius);
  margin: 5vh auto;
  width: 90% !important;
  max-width: 400px;
}

.el-dialog__header {
  padding: 16px 20px;
}

.el-dialog__body {
  padding: 0 20px 20px;
}

/* 表格样式优化 */
.el-table {
  border-radius: var(--border-radius);
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  font-weight: 600;
}

/* 标签样式优化 */
.el-tag {
  border-radius: 4px;
  font-size: 12px;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-1 { flex: 1; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }

.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }

.p-8 { padding: 8px; }
.p-16 { padding: 16px; }
.p-24 { padding: 24px; }

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 触摸反馈 */
.touch-feedback {
  transition: background-color 0.2s ease;
}

.touch-feedback:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--text-secondary);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.empty-state .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state-text {
  font-size: 14px;
  margin-bottom: 16px;
}

/* 响应式断点 */
@media (max-width: 375px) {
  .page-content {
    padding: 12px;
  }
  
  .card-header,
  .card-body {
    padding: 12px;
  }
}

@media (min-width: 768px) {
  .page-content {
    max-width: 768px;
    margin: 0 auto;
  }
}
