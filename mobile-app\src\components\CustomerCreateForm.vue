<template>
  <div class="customer-create-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="客户姓名" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入客户姓名"
          clearable
        />
      </el-form-item>

      <el-form-item label="联系电话" prop="phone">
        <el-input
          v-model="form.phone"
          placeholder="请输入联系电话"
          clearable
        />
      </el-form-item>

      <el-form-item label="收货地址">
        <el-input
          v-model="form.address"
          placeholder="请输入收货地址（可选）"
          type="textarea"
          :rows="3"
        />
      </el-form-item>

      <el-form-item label="备注信息">
        <el-input
          v-model="form.notes"
          placeholder="请输入备注信息（可选）"
          type="textarea"
          :rows="2"
        />
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="$emit('cancel')" style="flex: 1;">
        取消
      </el-button>
      <el-button 
        type="primary" 
        @click="handleSubmit"
        :loading="loading"
        style="flex: 1;"
      >
        创建客户
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { customerAPI } from '@/utils/api'

const emit = defineEmits(['success', 'cancel'])

const formRef = ref()
const loading = ref(false)

const form = ref({
  name: '',
  phone: '',
  address: '',
  notes: ''
})

const rules = {
  name: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
  } catch {
    return
  }

  loading.value = true
  try {
    const result = await customerAPI.createCustomer(form.value)
    if (result.success) {
      emit('success', result.data)
    } else {
      ElMessage.error(result.error || '创建客户失败')
    }
  } catch (error) {
    ElMessage.error('创建客户失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.customer-create-form {
  max-height: 60vh;
  overflow-y: auto;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-primary);
}
</style>
